/**
 * <PERSON><PERSON><PERSON>lo <PERSON> para gerenciar equipamentos
 * Responsável por listar, criar, editar e excluir equipamentos
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('Equipamentos.js carregado - Inicializando módulo...');

    // Inicializar o módulo de equipamentos
    EquipmentModule.init();

    // Prevenir duplicação de requisições
    preventDuplicateSubmissions();

    // Garantir que a função showModal esteja disponível globalmente
    if (typeof window.showModal !== 'function') {
        console.log('Definindo função showModal globalmente');
        window.showModal = function(modalId) {
            console.log('Chamando showModal para:', modalId);
            const modalElement = document.getElementById(modalId);
            if (modalElement) {
                const modal = new bootstrap.Modal(modalElement);
                modal.show();
            } else {
                console.error('Modal não encontrado:', modalId);
            }
        };
    }

    // Carregar tipos de equipamento quando o modal for aberto
    const newEquipmentModal = document.getElementById('newEquipmentModal');
    if (newEquipmentModal) {
        newEquipmentModal.addEventListener('show.bs.modal', function () {
            loadEquipmentTypes();
        });
    }
});

/**
 * Previne a duplicação de requisições ao salvar equipamentos
 */
function preventDuplicateSubmissions() {
    // Substituir os onclick inline por event listeners do JavaScript
    const newEquipmentBtn = document.querySelector('button[onclick="showModal(\'newEquipmentModal\')"]');
    if (newEquipmentBtn) {
        // Remover o atributo onclick para evitar chamadas duplicadas
        newEquipmentBtn.removeAttribute('onclick');
        // Adicionar event listener
        newEquipmentBtn.addEventListener('click', function() {
            EquipmentModule.showModal('newEquipmentModal');
        });
    }

    // Fazer o mesmo para o botão de salvar equipamento
    const saveEquipmentBtn = document.querySelector('button[onclick="saveEquipment()"]');
    if (saveEquipmentBtn) {
        // Remover o atributo onclick para evitar chamadas duplicadas
        saveEquipmentBtn.removeAttribute('onclick');
        // Adicionar event listener com proteção contra cliques múltiplos
        saveEquipmentBtn.addEventListener('click', function(event) {
            // Prevenir cliques múltiplos
            if (saveEquipmentBtn.classList.contains('processing')) {
                return;
            }

            // Marcar o botão como em processamento
            saveEquipmentBtn.classList.add('processing');
            saveEquipmentBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Salvando...';

            // Chamar a função de salvar
            EquipmentModule.saveEquipment();

            // Restaurar o botão após 2 segundos
            setTimeout(function() {
                saveEquipmentBtn.classList.remove('processing');
                saveEquipmentBtn.innerHTML = 'Salvar';
            }, 2000);
        });
    }
}

/**
 * Módulo principal para gerenciar equipamentos
 */
const EquipmentModule = (function() {
    // Armazena os equipamentos carregados
    let equipments = [];

    /**
     * Inicializa todas as funcionalidades
     */
    function init() {
        console.log('Inicializando módulo de Equipamentos...');
        setupEventListeners();

        // Carregar equipamentos se estiver na página correta
        if (document.getElementById('equipmentTableBody')) {
            loadEquipments();
        }

        // Carregar equipamentos no card da página principal
        if (document.getElementById('equipmentCardTableBody')) {
            loadEquipmentsForCard();
        }

        // Configurar preview de imagem
        setupImagePreview();
    }

    /**
     * Configura a pré-visualização da imagem
     */
    function setupImagePreview() {
        const imageInput = document.getElementById('equipmentImage');
        const previewImg = document.getElementById('previewImg');

        if (imageInput && previewImg) {
            imageInput.addEventListener('change', function() {
                const file = this.files[0];
                if (file) {
                    const reader = new FileReader();

                    reader.onload = function(e) {
                        previewImg.src = e.target.result;
                        previewImg.style.display = 'block';
                    }

                    reader.readAsDataURL(file);
                } else {
                    previewImg.src = '/static/images/no-image.png';
                    previewImg.style.display = 'none';
                }
            });
        }
    }

    /**
     * Configura os event listeners para elementos da página
     */
    function setupEventListeners() {
        // Configurar botão para gerenciar equipamentos na página principal
        const gerenciarEquipamentosBtn = document.getElementById('btnGerenciarEquipamentos');
        if (gerenciarEquipamentosBtn) {
            gerenciarEquipamentosBtn.addEventListener('click', function() {
                showModal('equipmentModal');
            });
        }

        // Nota: O event listener para o botão de salvar é configurado em preventDuplicateSubmissions()
    }

    /**
     * Carrega os equipamentos para o card na página principal
     */
    function loadEquipmentsForCard() {
        console.log('[EQUIPMENT] Iniciando carregamento de equipamentos para card');
        const tableBody = document.getElementById('equipmentCardTableBody');
        if (!tableBody) {
            console.log('[EQUIPMENT] Elemento equipmentCardTableBody não encontrado, ignorando');
            return;
        }

        // Exibir mensagem de carregamento
        tableBody.innerHTML = '<tr><td colspan="4" class="text-center"><i class="fas fa-spinner fa-spin me-2"></i>Carregando equipamentos...</td></tr>';

        // Obter o ID da filial do usuário (pode estar armazenado em localStorage ou em uma variável global)
        const branchId = localStorage.getItem('branch_id') || (window.User && window.User.BranchID ? window.User.BranchID : null);

        // Construir a URL da API com o parâmetro branch_id
        let url = '/api/equipments';
        if (branchId && branchId !== '0') {
            url += `?branch_id=${branchId}`;
            console.log(`[EQUIPMENT] Carregando equipamentos da filial ${branchId} para card`);
        } else {
            console.log('[EQUIPMENT] Carregando todos os equipamentos para card (sem filtro de filial)');
        }

        // Fazer requisição para a API
        console.log(`[EQUIPMENT] Fazendo requisição para ${url} (card)`);
        fetch(url)
            .then(response => {
                console.log(`[EQUIPMENT] Resposta da API (card): ${response.status}`);
                if (!response.ok) {
                    throw new Error(`Falha ao carregar equipamentos (${response.status})`);
                }
                return response.json();
            })
            .then(data => {
                console.log(`[EQUIPMENT] Equipamentos recebidos para card: ${data.length}`, data);
                // Armazenar os equipamentos
                equipments = data;

                if (data && data.length > 0) {
                    // Limpar a tabela
                    tableBody.innerHTML = '';

                    // Preencher com os dados (limitado a 3 equipamentos para o card)
                    const limitedData = data.slice(0, 3);
                    console.log(`[EQUIPMENT] Exibindo ${limitedData.length} equipamentos no card`);

                    limitedData.forEach(equipment => {
                        console.log(`[EQUIPMENT] Processando equipamento para card ID=${equipment.id}, Nome=${equipment.name}`);
                        const row = document.createElement('tr');

                        // Definir a classe de status
                        let statusClass = '';
                        if (equipment.status === 'ativo') {
                            statusClass = 'badge-shell-green';
                        } else if (equipment.status === 'manutencao') {
                            statusClass = 'badge-shell-yellow';
                        } else {
                            statusClass = 'badge-shell-red';
                        }

                        // Formatar o status para exibição
                        let statusText = '';
                        if (equipment.status === 'ativo') {
                            statusText = 'Ativo';
                        } else if (equipment.status === 'manutencao') {
                            statusText = 'Em Manutenção';
                        } else {
                            statusText = 'Inativo';
                        }

                        // Preencher as células
                        row.innerHTML = `
                            <td>${equipment.name}</td>
                            <td>${formatEquipmentType(equipment.type).split(' ')[0]}</td>
                            <td><span class="${statusClass}">${statusText}</span></td>
                            <td>
                                <button class="btn-icon" onclick="viewEquipment(${equipment.id})">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </td>
                        `;

                        tableBody.appendChild(row);
                    });

                    // Se houver mais equipamentos, mostrar uma linha indicando isso
                    if (data.length > 3) {
                        const moreRow = document.createElement('tr');
                        moreRow.innerHTML = `
                            <td colspan="4" class="text-center">
                                <small><i class="fas fa-info-circle me-1"></i> Mais ${data.length - 3} equipamento(s) disponível(is)</small>
                            </td>
                        `;
                        tableBody.appendChild(moreRow);
                    }

                    // Atualizar o ID do equipamento no card
                    const equipmentCard = document.querySelector('.equipment-card');
                    if (equipmentCard && data.length > 0) {
                        console.log(`[EQUIPMENT] Atualizando data-equipment-id do card para ${data[0].id}`);
                        equipmentCard.setAttribute('data-equipment-id', data[0].id);
                    }

                    console.log('[EQUIPMENT] Card de equipamentos preenchido com sucesso');
                } else {
                    // Sem equipamentos
                    console.log('[EQUIPMENT] Nenhum equipamento encontrado para o card');
                    tableBody.innerHTML = '<tr><td colspan="4" class="text-center">Nenhum equipamento cadastrado.</td></tr>';
                }
            })
            .catch(error => {
                console.error('[EQUIPMENT-ERROR] Erro ao carregar equipamentos para card:', error);
                tableBody.innerHTML = `<tr><td colspan="4" class="text-center text-danger"><i class="fas fa-exclamation-circle me-2"></i>Erro ao carregar equipamentos: ${error.message}</td></tr>`;
            });
    }

    /**
     * Carrega a lista de equipamentos da filial
     */
    function loadEquipments() {
        console.log('[EQUIPMENT] Iniciando carregamento de equipamentos');
        const tableBody = document.getElementById('equipmentTableBody');
        if (!tableBody) {
            console.error('[EQUIPMENT-ERROR] Elemento equipmentTableBody não encontrado');
            return;
        }

        // Exibir mensagem de carregamento
        tableBody.innerHTML = '<tr><td colspan="7" class="text-center"><i class="fas fa-spinner fa-spin me-2"></i>Carregando equipamentos...</td></tr>';

        // Obter o ID da filial do usuário (pode estar armazenado em localStorage ou em uma variável global)
        const branchId = localStorage.getItem('branch_id') || (window.User && window.User.BranchID ? window.User.BranchID : null);

        // Construir a URL da API com o parâmetro branch_id
        let url = '/api/equipments';
        if (branchId && branchId !== '0') {
            url += `?branch_id=${branchId}`;
            console.log(`[EQUIPMENT] Carregando equipamentos da filial ${branchId}`);
        } else {
            console.log('[EQUIPMENT] Carregando todos os equipamentos (sem filtro de filial)');
        }

        // Fazer requisição para a API
        console.log(`[EQUIPMENT] Fazendo requisição para ${url}`);
        fetch(url)
            .then(response => {
                console.log(`[EQUIPMENT] Resposta da API: ${response.status}`);
                if (!response.ok) {
                    throw new Error(`Falha ao carregar equipamentos (${response.status})`);
                }
                return response.json();
            })
            .then(data => {
                console.log(`[EQUIPMENT] Equipamentos recebidos: ${data.length}`, data);
                // Armazenar os equipamentos
                equipments = data;

                if (data && data.length > 0) {
                    // Limpar a tabela
                    tableBody.innerHTML = '';

                    // Preencher com os dados
                    data.forEach(equipment => {
                        console.log(`[EQUIPMENT] Processando equipamento ID=${equipment.id}, Nome=${equipment.name}`);
                        const row = document.createElement('tr');

                        // Definir a classe de status
                        let statusClass = '';
                        if (equipment.status === 'ativo') {
                            statusClass = 'badge-shell-green';
                        } else if (equipment.status === 'manutencao') {
                            statusClass = 'badge-shell-yellow';
                        } else {
                            statusClass = 'badge-shell-red';
                        }

                        // Formatar o status para exibição
                        let statusText = '';
                        if (equipment.status === 'ativo') {
                            statusText = 'Ativo';
                        } else if (equipment.status === 'manutencao') {
                            statusText = 'Em Manutenção';
                        } else {
                            statusText = 'Inativo';
                        }

                        // Preencher as células
                        row.innerHTML = `
                            <td>${equipment.id}</td>
                            <td>${equipment.name}</td>
                            <td>${formatEquipmentType(equipment.type)}</td>
                            <td>${equipment.model || '-'}</td>
                            <td>${equipment.serial_number || '-'}</td>
                            <td><span class="${statusClass}">${statusText}</span></td>
                            <td>
                                <button class="btn-icon" onclick="viewEquipment(${equipment.id})" data-bs-toggle="tooltip" title="Ver Detalhes">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn-icon" onclick="editEquipment(${equipment.id})" data-bs-toggle="tooltip" title="Editar">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn-icon" onclick="confirmDeleteEquipment(${equipment.id})" data-bs-toggle="tooltip" title="Excluir">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                            </td>
                        `;

                        tableBody.appendChild(row);
                    });

                    console.log('[EQUIPMENT] Tabela de equipamentos preenchida com sucesso');

                    // Reinicializar tooltips
                    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                    tooltipTriggerList.map(function (tooltipTriggerEl) {
                        return new bootstrap.Tooltip(tooltipTriggerEl);
                    });
                } else {
                    // Sem equipamentos
                    console.log('[EQUIPMENT] Nenhum equipamento encontrado');
                    tableBody.innerHTML = '<tr><td colspan="7" class="text-center">Nenhum equipamento cadastrado.</td></tr>';
                }
            })
            .catch(error => {
                console.error('[EQUIPMENT-ERROR] Erro ao carregar equipamentos:', error);
                tableBody.innerHTML = `<tr><td colspan="7" class="text-center text-danger"><i class="fas fa-exclamation-circle me-2"></i>Erro ao carregar equipamentos: ${error.message}</td></tr>`;
            });
    }

    /**
     * Formata o tipo de equipamento para exibição
     * @param {string} type - Tipo do equipamento
     * @returns {string} - Tipo formatado
     */
    function formatEquipmentType(type) {
        const types = {
            'bomba': 'Bomba de Combustível',
            'tanque': 'Tanque de Armazenamento',
            'compressor': 'Compressor de Ar',
            'gerador': 'Gerador de Energia',
            'sistema_pagamento': 'Sistema de Pagamento',
            'outro': 'Outro'
        };

        return types[type] || type;
    }

    /**
     * Visualiza os detalhes de um equipamento
     * @param {number} id - ID do equipamento
     */
    function viewEquipment(id) {
        console.log('Visualizar equipamento:', id);

        // Fazer requisição para a API
        fetch(`/api/equipments/${id}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Falha ao carregar detalhes do equipamento');
                }
                return response.json();
            })
            .then(data => {
                // Encontrar o card do equipamento na página principal
                const equipmentCard = document.querySelector(`.equipment-card[data-equipment-id="${id}"]`);

                if (equipmentCard) {
                    // Se estamos na página principal, usar o efeito flip
                    const cardContainer = equipmentCard.closest('.equipment-card-container');

                    if (cardContainer) {
                        // Preencher os detalhes no verso do card
                        const backCard = cardContainer.querySelector('.equipment-card-back');

                        if (backCard) {
                            // Formatar o tipo de equipamento
                            const equipmentType = formatEquipmentType(data.type);

                            // Formatar as datas
                            const installationDate = data.installation_date ? new Date(data.installation_date).toLocaleDateString('pt-BR') : 'Não informada';
                            const lastMaintenance = data.last_maintenance ? new Date(data.last_maintenance).toLocaleDateString('pt-BR') : 'Não informada';

                            // Preencher o conteúdo do card
                            backCard.innerHTML = `
                                <div class="equipment-detail-header">
                                    <h3 class="equipment-detail-title">${data.name}</h3>
                                    <span class="badge-shell-${data.status === 'ativo' ? 'green' : (data.status === 'manutencao' ? 'yellow' : 'red')}">
                                        ${data.status === 'ativo' ? 'Ativo' : (data.status === 'manutencao' ? 'Em Manutenção' : 'Inativo')}
                                    </span>
                                </div>

                                <div class="equipment-detail-content">
                                    <div class="equipment-detail-item">
                                        <span class="equipment-detail-label">Tipo</span>
                                        <span class="equipment-detail-value">${equipmentType}</span>
                                    </div>

                                    <div class="equipment-detail-item">
                                        <span class="equipment-detail-label">Modelo</span>
                                        <span class="equipment-detail-value">${data.model || 'Não informado'}</span>
                                    </div>

                                    <div class="equipment-detail-item">
                                        <span class="equipment-detail-label">Fabricante</span>
                                        <span class="equipment-detail-value">${data.brand || 'Não informado'}</span>
                                    </div>

                                    <div class="equipment-detail-item">
                                        <span class="equipment-detail-label">Número de Série</span>
                                        <span class="equipment-detail-value">${data.serial_number || 'Não informado'}</span>
                                    </div>

                                    <div class="equipment-detail-item">
                                        <span class="equipment-detail-label">Data de Instalação</span>
                                        <span class="equipment-detail-value">${installationDate}</span>
                                    </div>

                                    <div class="equipment-detail-item">
                                        <span class="equipment-detail-label">Última Manutenção</span>
                                        <span class="equipment-detail-value">${lastMaintenance}</span>
                                    </div>
                                </div>

                                <div class="equipment-detail-image-container">
                                    <img src="/static/images/shell-pump.svg" alt="${data.name}" class="equipment-detail-image">
                                </div>

                                ${data.notes ? `
                                <div class="equipment-detail-notes mt-3">
                                    <span class="equipment-detail-label">Observações</span>
                                    <p class="equipment-detail-value">${data.notes}</p>
                                </div>
                                ` : ''}

                                <div class="equipment-detail-footer">
                                    <button class="btn-flip-back" onclick="flipCardBack(${id})">
                                        <i class="fas fa-arrow-left me-1"></i> Voltar para Lista
                                    </button>
                                </div>
                            `;

                            // Aplicar o efeito flip
                            cardContainer.classList.add('flipped');
                        }
                    }
                } else {
                    // Se não estamos na página principal, mostrar em um modal
                    console.log('[EQUIPMENT] Exibindo equipamento em modal');

                    // Formatar o tipo de equipamento
                    const equipmentType = formatEquipmentType(data.type);

                    // Formatar as datas
                    const installationDate = data.installation_date ? new Date(data.installation_date).toLocaleDateString('pt-BR') : 'Não informada';
                    const lastMaintenance = data.last_maintenance ? new Date(data.last_maintenance).toLocaleDateString('pt-BR') : 'Não informada';
                    const acquisitionDate = data.acquisition_date ? new Date(data.acquisition_date).toLocaleDateString('pt-BR') : 'Não informada';

                    // Criar o conteúdo do modal
                    const modalContent = `
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-info-circle text-shell-yellow me-2"></i>
                                Detalhes do Equipamento
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                        </div>
                        <div class="modal-body">
                            <div class="equipment-detail-header">
                                <h3 class="equipment-detail-title">${data.name}</h3>
                                <span class="badge-shell-${data.status === 'ativo' ? 'green' : (data.status === 'manutencao' ? 'yellow' : 'red')}">
                                    ${data.status === 'ativo' ? 'Ativo' : (data.status === 'manutencao' ? 'Em Manutenção' : 'Inativo')}
                                </span>
                            </div>

                            <div class="row mt-3">
                                <div class="col-md-7">
                                    <div class="equipment-detail-content">
                                        <div class="equipment-detail-item">
                                            <span class="equipment-detail-label">Tipo</span>
                                            <span class="equipment-detail-value">${equipmentType}</span>
                                        </div>

                                        <div class="equipment-detail-item">
                                            <span class="equipment-detail-label">Modelo</span>
                                            <span class="equipment-detail-value">${data.model || 'Não informado'}</span>
                                        </div>

                                        <div class="equipment-detail-item">
                                            <span class="equipment-detail-label">Fabricante</span>
                                            <span class="equipment-detail-value">${data.brand || 'Não informado'}</span>
                                        </div>

                                        <div class="equipment-detail-item">
                                            <span class="equipment-detail-label">Número de Série</span>
                                            <span class="equipment-detail-value">${data.serial_number || 'Não informado'}</span>
                                        </div>

                                        <div class="equipment-detail-item">
                                            <span class="equipment-detail-label">Data de Aquisição</span>
                                            <span class="equipment-detail-value">${acquisitionDate}</span>
                                        </div>

                                        <div class="equipment-detail-item">
                                            <span class="equipment-detail-label">Data de Instalação</span>
                                            <span class="equipment-detail-value">${installationDate}</span>
                                        </div>

                                        <div class="equipment-detail-item">
                                            <span class="equipment-detail-label">Última Manutenção</span>
                                            <span class="equipment-detail-value">${lastMaintenance}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-5 text-center">
                                    <img src="/static/images/shell-pump.svg" alt="${data.name}" class="img-fluid" style="max-height: 200px;">
                                </div>
                            </div>

                            ${data.notes ? `
                            <div class="equipment-detail-notes mt-3">
                                <span class="equipment-detail-label">Observações</span>
                                <p class="equipment-detail-value">${data.notes}</p>
                            </div>
                            ` : ''}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn-shell" data-bs-dismiss="modal">Fechar</button>
                            <button type="button" class="btn-shell-yellow" onclick="editEquipment(${id})" data-bs-dismiss="modal">Editar</button>
                        </div>
                    `;

                    // Criar um modal dinâmico
                    const modalElement = document.createElement('div');
                    modalElement.className = 'modal fade';
                    modalElement.id = 'equipmentDetailModal';
                    modalElement.innerHTML = `
                        <div class="modal-dialog modal-dialog-centered modal-lg">
                            <div class="modal-content modal-shell">
                                ${modalContent}
                            </div>
                        </div>
                    `;

                    // Adicionar o modal ao documento
                    document.body.appendChild(modalElement);

                    // Exibir o modal
                    const modal = new bootstrap.Modal(modalElement);
                    modal.show();

                    // Remover o modal quando for fechado
                    modalElement.addEventListener('hidden.bs.modal', function() {
                        document.body.removeChild(modalElement);
                    });
                }
            })
            .catch(error => {
                console.error('Erro ao carregar detalhes do equipamento:', error);
                alert(`Erro ao carregar detalhes: ${error.message}`);
            });
    }

    /**
     * Volta o card para a frente
     * @param {number} id - ID do equipamento
     */
    function flipCardBack(id) {
        const equipmentCard = document.querySelector(`.equipment-card[data-equipment-id="${id}"]`);
        if (equipmentCard) {
            const cardContainer = equipmentCard.closest('.equipment-card-container');
            if (cardContainer) {
                cardContainer.classList.remove('flipped');
            }
        }
    }

    /**
     * Edita um equipamento
     * @param {number} id - ID do equipamento
     */
    function editEquipment(id) {
        console.log('Editar equipamento:', id);

        // Fazer requisição para a API
        fetch(`/api/equipments/${id}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Falha ao carregar dados do equipamento');
                }
                return response.json();
            })
            .then(data => {
                // Preencher o formulário com os dados
                document.getElementById('equipmentName').value = data.name;
                document.getElementById('equipmentModel').value = data.model || '';
                document.getElementById('equipmentBrand').value = data.brand || '';
                document.getElementById('equipmentSerialNumber').value = data.serial_number || '';
                document.getElementById('equipmentStatus').value = data.status;
                document.getElementById('equipmentNotes').value = data.notes || '';

                // Converter select de tipo em campo de texto somente leitura para edição
                const equipmentTypeSelect = document.getElementById('equipmentType');
                const equipmentTypeContainer = equipmentTypeSelect.parentElement;

                // Criar campo de texto para exibir o tipo
                const equipmentTypeText = document.createElement('input');
                equipmentTypeText.type = 'text';
                equipmentTypeText.className = 'form-control';
                equipmentTypeText.id = 'equipmentTypeText';
                equipmentTypeText.name = 'type_display';
                equipmentTypeText.value = formatEquipmentType(data.type);
                equipmentTypeText.readOnly = true;
                equipmentTypeText.style.backgroundColor = '#f8f9fa';
                equipmentTypeText.style.cursor = 'not-allowed';

                // Criar campo hidden para manter o valor original
                const equipmentTypeHidden = document.createElement('input');
                equipmentTypeHidden.type = 'hidden';
                equipmentTypeHidden.id = 'equipmentTypeHidden';
                equipmentTypeHidden.name = 'type';
                equipmentTypeHidden.value = data.type;

                // Substituir select por texto + hidden
                equipmentTypeSelect.style.display = 'none';
                equipmentTypeContainer.appendChild(equipmentTypeText);
                equipmentTypeContainer.appendChild(equipmentTypeHidden);

                // Adicionar ID ao formulário para identificar que é uma edição
                const form = document.getElementById('equipmentForm');
                form.dataset.equipmentId = id;

                // Alterar o título do modal
                document.getElementById('newEquipmentModalLabel').innerHTML = '<i class="fas fa-edit text-shell-yellow me-2"></i>Editar Equipamento';

                // Exibir o modal
                showModal('newEquipmentModal');
            })
            .catch(error => {
                console.error('Erro ao carregar dados do equipamento:', error);
                alert(`Erro ao carregar dados: ${error.message}`);
            });
    }

    /**
     * Confirma a exclusão de um equipamento
     * @param {number} id - ID do equipamento
     */
    function confirmDeleteEquipment(id) {
        if (confirm('Tem certeza que deseja excluir este equipamento? Esta ação não pode ser desfeita.')) {
            deleteEquipment(id);
        }
    }

    /**
     * Exclui um equipamento
     * @param {number} id - ID do equipamento
     */
    function deleteEquipment(id) {
        // Fazer requisição para a API
        fetch(`/api/equipments/${id}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Falha ao excluir equipamento');
            }
            return response.json();
        })
        .then(data => {
            // Recarregar a lista de equipamentos
            loadEquipments();

            // Exibir mensagem de sucesso
            alert('Equipamento excluído com sucesso!');
        })
        .catch(error => {
            console.error('Erro ao excluir equipamento:', error);
            alert(`Erro ao excluir equipamento: ${error.message}`);
        });
    }

    /**
     * Salva um equipamento (novo ou edição)
     */
    function saveEquipment() {
        console.log('[EQUIPMENT] Iniciando saveEquipment()');

        // Verificar se já está processando uma requisição
        const btnSalvar = document.getElementById('btnSalvarEquipamento');
        if (btnSalvar && btnSalvar.classList.contains('processing')) {
            console.log('[EQUIPMENT] Botão já está processando, ignorando clique');
            return;
        }

        // Marcar botão como processando
        if (btnSalvar) {
            btnSalvar.classList.add('processing');
            btnSalvar.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Salvando...';
            btnSalvar.disabled = true;
        }

        // Obter o formulário
        const form = document.getElementById('equipmentForm');

        // Verificar se é uma edição ou um novo equipamento
        const isEdit = form.dataset.equipmentId !== undefined;
        console.log(`[EQUIPMENT] Modo: ${isEdit ? 'Edição' : 'Criação'}`);

        // Verificar se há uma imagem para upload
        const imageInput = document.getElementById('equipmentImage');
        const hasImage = imageInput && imageInput.files && imageInput.files.length > 0;

        // URL e método da requisição
        let url = '/api/equipments';
        let method = 'POST';

        if (isEdit) {
            url = `/api/equipments/${form.dataset.equipmentId}`;
            method = 'PUT';
        }

        let requestPromise;

        // Se houver imagem, usar FormData para enviar o arquivo
        if (hasImage) {
            console.log('[EQUIPMENT] Enviando com imagem via FormData');
            // Criar um objeto FormData para enviar arquivos
            const formData = new FormData();

            // Adicionar os campos do formulário
            formData.append('name', document.getElementById('equipmentName').value);

            // Obter valor do tipo (hidden se estiver em modo edição, select se criação)
            const typeValue = document.getElementById('equipmentTypeHidden')
                ? document.getElementById('equipmentTypeHidden').value
                : document.getElementById('equipmentType').value;
            formData.append('type', typeValue);

            formData.append('model', document.getElementById('equipmentModel').value);
            formData.append('brand', document.getElementById('equipmentBrand').value);
            formData.append('serial_number', document.getElementById('equipmentSerialNumber').value);
            formData.append('status', document.getElementById('equipmentStatus').value);
            formData.append('notes', document.getElementById('equipmentNotes').value);

            // Adicionar a imagem
            formData.append('image', imageInput.files[0]);

            // Validar campos obrigatórios
            if (!formData.get('name') || !formData.get('type') || !formData.get('status')) {
                alert('Por favor, preencha todos os campos obrigatórios.');
                resetSaveButton(btnSalvar);
                return;
            }

            // Fazer requisição para a API
            requestPromise = fetch(url, {
                method: method,
                body: formData // Não definir Content-Type para que o navegador defina automaticamente com boundary
            });
        } else {
            console.log('[EQUIPMENT] Enviando sem imagem via JSON');
            // Sem imagem, usar JSON normal
            // Obter valor do tipo (hidden se estiver em modo edição, select se criação)
            const typeValue = document.getElementById('equipmentTypeHidden')
                ? document.getElementById('equipmentTypeHidden').value
                : document.getElementById('equipmentType').value;

            const formData = {
                name: document.getElementById('equipmentName').value,
                type: typeValue,
                model: document.getElementById('equipmentModel').value,
                brand: document.getElementById('equipmentBrand').value,
                serial_number: document.getElementById('equipmentSerialNumber').value,
                status: document.getElementById('equipmentStatus').value,
                notes: document.getElementById('equipmentNotes').value
            };

            // Validar campos obrigatórios
            if (!formData.name || !formData.type || !formData.status) {
                alert('Por favor, preencha todos os campos obrigatórios.');
                resetSaveButton(btnSalvar);
                return;
            }

            // Fazer requisição para a API
            requestPromise = fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });
        }

        // Processar a resposta da requisição
        requestPromise
            .then(response => {
                console.log(`[EQUIPMENT] Resposta da API: ${response.status}`);
                if (!response.ok) {
                    throw new Error(`Falha ao salvar equipamento (${response.status})`);
                }
                return response.json();
            })
            .then(data => {
                console.log('[EQUIPMENT] Equipamento salvo com sucesso:', data);
                // Recarregar a lista de equipamentos
                loadEquipments();

                // Também recarregar a lista de equipamentos no card da página principal
                if (typeof loadEquipmentsForCard === 'function') {
                    loadEquipmentsForCard();
                }

                // Fechar o modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('newEquipmentModal'));
                if (modal) {
                    modal.hide();
                }

                // Limpar o formulário
                form.reset();
                delete form.dataset.equipmentId;

                // Limpar campos dinâmicos de edição
                const equipmentTypeText = document.getElementById('equipmentTypeText');
                const equipmentTypeHidden = document.getElementById('equipmentTypeHidden');
                if (equipmentTypeText) {
                    equipmentTypeText.remove();
                }
                if (equipmentTypeHidden) {
                    equipmentTypeHidden.remove();
                }

                // Restaurar select original
                const equipmentTypeSelect = document.getElementById('equipmentType');
                equipmentTypeSelect.style.display = 'block';

                // Restaurar o título do modal
                const modalTitle = document.getElementById('newEquipmentModalLabel');
                if (modalTitle) {
                    modalTitle.innerHTML = '<i class="fas fa-plus-circle text-shell-yellow me-2"></i>Cadastrar Novo Equipamento';
                }

                // Exibir mensagem de sucesso
                alert(isEdit ? 'Equipamento atualizado com sucesso!' : 'Equipamento cadastrado com sucesso!');
            })
            .catch(error => {
                console.error('[EQUIPMENT-ERROR] Erro ao salvar equipamento:', error);
                alert(`Erro ao salvar equipamento: ${error.message}`);
            })
            .finally(() => {
                // Restaurar o botão independentemente do resultado
                resetSaveButton(btnSalvar);
            });
    }

    /**
     * Restaura o estado do botão de salvar
     * @param {HTMLElement} button - O botão a ser restaurado
     */
    function resetSaveButton(button) {
        if (button) {
            button.classList.remove('processing');
            button.innerHTML = 'Salvar';
            button.disabled = false;
        }
    }

    /**
     * Exibe um modal pelo ID
     * @param {string} modalId - ID do modal a ser exibido
     */
    function showModal(modalId) {
        console.log('EquipmentModule.showModal chamado para:', modalId);
        const modalElement = document.getElementById(modalId);
        if (modalElement) {
            console.log('Modal encontrado, exibindo...');

            // Verificar se o modal já está aberto
            const existingModal = bootstrap.Modal.getInstance(modalElement);
            if (existingModal) {
                console.log('Modal já está aberto, fechando primeiro...');
                existingModal.dispose();
            }

            // Remover backdrop se existir
            const backdrop = document.querySelector('.modal-backdrop');
            if (backdrop) {
                backdrop.parentNode.removeChild(backdrop);
            }

            // Remover classes que podem estar travando a interface
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';

            // Criar e mostrar o modal
            const modal = new bootstrap.Modal(modalElement, {
                backdrop: 'static',
                keyboard: true
            });
            modal.show();
        } else {
            console.error('Modal não encontrado:', modalId);
        }
    }

    // Retorna funções públicas
    return {
        init,
        loadEquipments,
        loadEquipmentsForCard,
        viewEquipment,
        editEquipment,
        confirmDeleteEquipment,
        deleteEquipment,
        saveEquipment,
        showModal,
        flipCardBack
    };
})();

/**
 * Carrega os equipamentos como cards responsivos
 */
function loadEquipmentsAsCards() {
    console.log('[EQUIPMENT] Iniciando carregamento de equipamentos como cards');
    const container = document.getElementById('equipmentCardContainer');
    if (!container) {
        console.log('[EQUIPMENT] Elemento equipmentCardContainer não encontrado, ignorando');
        return;
    }

    // Exibir mensagem de carregamento
    container.innerHTML = `
        <div class="col-12 text-center py-5">
            <div class="spinner-border text-shell-yellow" role="status">
                <span class="visually-hidden">Carregando...</span>
            </div>
            <p class="mt-3">Carregando equipamentos...</p>
        </div>
    `;

    // Obter o ID da filial do usuário (pode estar armazenado em localStorage ou em uma variável global)
    const branchId = localStorage.getItem('branch_id') || (window.User && window.User.BranchID ? window.User.BranchID : null);

    // Construir a URL da API com o parâmetro branch_id
    let url = '/api/equipments';
    if (branchId && branchId !== '0') {
        url += `?branch_id=${branchId}`;
        console.log(`[EQUIPMENT] Carregando equipamentos da filial ${branchId} para cards`);
    } else {
        console.log('[EQUIPMENT] Carregando todos os equipamentos para cards (sem filtro de filial)');
    }

    // Fazer requisição para a API
    console.log(`[EQUIPMENT] Fazendo requisição para ${url} (cards)`);
    fetch(url)
        .then(response => {
            console.log(`[EQUIPMENT] Resposta da API (cards): ${response.status}`);
            if (!response.ok) {
                throw new Error(`Falha ao carregar equipamentos (${response.status})`);
            }
            return response.json();
        })
        .then(data => {
            console.log(`[EQUIPMENT] Equipamentos recebidos para cards: ${data.length}`, data);

            if (data && data.length > 0) {
                // Limpar o container
                container.innerHTML = '';

                // Preencher com os dados
                data.forEach(equipment => {
                    console.log(`[EQUIPMENT] Processando equipamento para card ID=${equipment.id}, Nome=${equipment.name}`);

                    // Definir a classe de status
                    let statusClass = '';
                    let statusText = '';

                    if (equipment.status === 'ativo') {
                        statusClass = 'badge-shell-green';
                        statusText = 'Ativo';
                    } else if (equipment.status === 'manutencao') {
                        statusClass = 'badge-shell-yellow';
                        statusText = 'Em Manutenção';
                    } else {
                        statusClass = 'badge-shell-red';
                        statusText = 'Inativo';
                    }

                    // Formatar o tipo de equipamento para exibição
                    const equipmentTypeFormatted = formatEquipmentType(equipment.type);

                    // Criar o card
                    const cardCol = document.createElement('div');
                    cardCol.className = 'col-lg-3 col-md-4 col-sm-6 mb-4';

                    cardCol.innerHTML = `
                        <div class="card-shell equipment-card-responsive" onclick="viewEquipment(${equipment.id})">
                            <div class="card-header-shell">
                                <h3 class="card-title">${equipment.name}</h3>
                                <span class="${statusClass}">${statusText}</span>
                            </div>
                            <div class="card-body-shell">
                                <div class="equipment-image">
                                    <img src="/static/images/shell-pump.svg" alt="${equipment.name}">
                                </div>
                                <div class="equipment-details">
                                    <div class="equipment-detail-item">
                                        <span class="equipment-detail-label">Tipo</span>
                                        <span class="equipment-detail-value">${equipmentTypeFormatted}</span>
                                    </div>
                                    <div class="equipment-detail-item">
                                        <span class="equipment-detail-label">Modelo</span>
                                        <span class="equipment-detail-value">${equipment.model || 'Não informado'}</span>
                                    </div>
                                </div>
                                <div class="equipment-actions">
                                    <button class="btn-shell-red btn-sm" onclick="event.stopPropagation(); viewEquipment(${equipment.id})">
                                        <i class="fas fa-eye me-1"></i> Detalhes
                                    </button>
                                    <button class="btn-shell btn-sm" onclick="event.stopPropagation(); editEquipment(${equipment.id})">
                                        <i class="fas fa-edit me-1"></i> Editar
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;

                    container.appendChild(cardCol);
                });

                console.log('[EQUIPMENT] Cards de equipamentos preenchidos com sucesso');
            } else {
                // Sem equipamentos
                console.log('[EQUIPMENT] Nenhum equipamento encontrado para cards');
                container.innerHTML = `
                    <div class="col-12 text-center py-5">
                        <div class="alert-shell alert-shell-warning">
                            <i class="fas fa-info-circle me-2"></i> Nenhum equipamento cadastrado.
                        </div>
                        <button class="btn-shell-yellow mt-3" id="btnNovoEquipamentoEmpty" onclick="showModal('newEquipmentModal')">
                            <i class="fas fa-plus-circle me-2"></i>
                            Cadastrar Novo Equipamento
                        </button>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('[EQUIPMENT-ERROR] Erro ao carregar equipamentos para cards:', error);
            container.innerHTML = `
                <div class="col-12 text-center py-5">
                    <div class="alert-shell alert-shell-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        Erro ao carregar equipamentos: ${error.message}
                    </div>
                    <button class="btn-shell mt-3" onclick="loadEquipmentsAsCards()">
                        <i class="fas fa-sync-alt me-2"></i>
                        Tentar Novamente
                    </button>
                </div>
            `;
        });
}

// Tornando funções disponíveis globalmente para uso em atributos onclick
window.viewEquipment = EquipmentModule.viewEquipment;
window.editEquipment = EquipmentModule.editEquipment;
window.confirmDeleteEquipment = EquipmentModule.confirmDeleteEquipment;
window.deleteEquipment = EquipmentModule.deleteEquipment;
window.saveEquipment = EquipmentModule.saveEquipment;
window.showModal = EquipmentModule.showModal; // Garantir que esta função esteja disponível globalmente
window.flipCardBack = EquipmentModule.flipCardBack;
window.loadEquipmentsAsCards = loadEquipmentsAsCards; // Nova função para carregar equipamentos como cards

// Verificar se as funções foram expostas corretamente
console.log('Funções expostas globalmente:', {
    viewEquipment: typeof window.viewEquipment === 'function',
    editEquipment: typeof window.editEquipment === 'function',
    confirmDeleteEquipment: typeof window.confirmDeleteEquipment === 'function',
    deleteEquipment: typeof window.deleteEquipment === 'function',
    saveEquipment: typeof window.saveEquipment === 'function',
    showModal: typeof window.showModal === 'function',
    flipCardBack: typeof window.flipCardBack === 'function',
    loadEquipmentsAsCards: typeof window.loadEquipmentsAsCards === 'function'
});

/**
 * Carrega os tipos de equipamento do backend e popula o select
 */
function loadEquipmentTypes() {
    console.log('[EQUIPMENT] Iniciando carregamento de tipos de equipamento');
    const select = document.getElementById('equipmentType');
    if (!select) {
        console.error('[EQUIPMENT] Elemento equipmentType não encontrado');
        return;
    }

    // Exibir loading
    select.innerHTML = '<option value="">Carregando tipos...</option>';
    select.disabled = true;

    // Fazer requisição para a API
    fetch('/api/equipment-types')
        .then(response => {
            console.log(`[EQUIPMENT] Resposta da API de tipos: ${response.status}`);
            if (!response.ok) {
                throw new Error(`Falha ao buscar tipos de equipamento (${response.status})`);
            }
            return response.json();
        })
        .then(response => {
            // Verificar se a resposta tem a estrutura esperada
            if (!response.success || !response.data) {
                throw new Error('Resposta inválida do servidor');
            }

            const types = response.data;
            console.log(`[EQUIPMENT] ${types.length} tipos recebidos:`, types);

            // Limpar e popular o select
            select.innerHTML = '<option value="">Selecione o tipo</option>';

            if (types && Array.isArray(types) && types.length > 0) {
                types.forEach(type => {
                    const option = document.createElement('option');
                    option.value = type.name;
                    option.textContent = type.name + (type.description ? ` - ${type.description}` : '');
                    select.appendChild(option);
                });
                select.disabled = false;
                console.log('[EQUIPMENT] Select de tipos populado com sucesso');
            } else {
                select.innerHTML = '<option value="">Nenhum tipo cadastrado</option>';
                select.disabled = true;
                console.warn('[EQUIPMENT] Nenhum tipo de equipamento encontrado');
            }
        })
        .catch(error => {
            console.error('[EQUIPMENT-ERROR] Erro ao carregar tipos:', error);
            select.innerHTML = '<option value="">Erro ao carregar tipos</option>';
            select.disabled = true;
            // Notificar o usuário
            const errorMsg = `Erro ao carregar tipos de equipamento: ${error.message}`;
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    icon: 'error',
                    title: 'Erro',
                    text: errorMsg,
                    confirmButtonColor: '#ffbe0b'
                });
            } else {
                alert(errorMsg);
            }
        });
}
