{{ define "minhaconta/minha_conta.html" }}
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ .title }}</title>
    <link rel="icon" href="/static/images/favicon.ico" type="image/x-icon">

    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="/static/css/common.css">
    <link rel="stylesheet" href="/static/css/styles.css">

    <!-- Fontes -->
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Share+Tech+Mono&display=swap" rel="stylesheet">

    <!-- CSS Específico -->
    <link rel="stylesheet" href="/static/css/sidebar.css">
    <link rel="stylesheet" href="/static/css/minha_conta.css">
</head>
<body class="bg-dark">
    <!-- Sidebar -->
    {{ template "sidebar" . }}

    <!-- Conteúdo principal -->
    <div class="content-with-sidebar">
        <div class="container-fluid py-4">
            <!-- Cabeçalho da página -->
            <div class="page-header mb-4">
                <div class="d-flex align-items-center mb-2">
                    <i class="fas fa-user-circle fa-2x text-shell-red me-3"></i>
                    <h1 class="page-title m-0">Minha Conta</h1>
                </div>
                <p class="page-subtitle">
                    Gerencie suas informações pessoais e acompanhe as atividades associadas ao seu perfil.
                </p>
            </div>

            <!-- Grid de cards -->
            <div class="row g-4">
                <!-- Card de Perfil -->
                <div class="col-lg-4 col-md-6">
                    <div class="card-shell profile-card">
                        <div class="card-header-shell">
                            <i class="fas fa-id-card text-shell-yellow me-2"></i>
                            <h2 class="card-title">Perfil</h2>
                        </div>
                        <div class="card-body-shell">
                            <!-- Foto de perfil removida -->

                            <div class="info-grid">
                                <div class="info-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <span class="info-label">
                                            <i class="fas fa-building text-shell-yellow me-2"></i>Filial
                                        </span>
                                        <div class="info-value">{{ if .User }}{{ .User.Name }}{{ else }}Filial{{ end }}</div>
                                    </div>
                                    <div class="text-end">
                                        <span class="info-label">
                                            <i class="fas fa-briefcase text-shell-yellow me-2"></i>Função
                                        </span>
                                        <div class="info-value">
                                            {{ if .User }}
                                                {{ if eq .User.Role "admin" }}
                                                    <span class="badge-shell badge-shell-yellow">Administrador</span>
                                                {{ else if eq .User.Role "gerente" }}
                                                    <span class="badge-shell badge-shell-yellow">Gerente</span>
                                                {{ else if eq .User.Role "financeiro" }}
                                                    <span class="badge-shell badge-shell-yellow">Financeiro</span>
                                                {{ else if eq .User.Role "tecnico" }}
                                                    <span class="badge-shell badge-shell-yellow">Técnico</span>
                                                {{ else if eq .User.Role "filial" }}
                                                    <span class="badge-shell badge-shell-yellow">Filial</span>
                                                {{ else if eq .User.Role "prestadores" }}
                                                    <span class="badge-shell badge-shell-yellow">Prestador de Serviços</span>
                                                {{ else }}
                                                    <span class="badge-shell badge-shell-yellow">{{ .User.Role }}</span>
                                                {{ end }}
                                            {{ else }}
                                                <span class="badge-shell badge-shell-yellow">Usuário</span>
                                            {{ end }}
                                        </div>
                                    </div>
                                </div>

                                <div class="info-item">
                                    <span class="info-label">
                                        <i class="fas fa-envelope text-shell-yellow me-2"></i>E-mail
                                    </span>
                                    <div class="info-value">{{ if .User }}{{ .User.Email }}{{ else }}<EMAIL>{{ end }}</div>
                                </div>
                            </div>

                            <!-- Informações de segurança integradas ao perfil -->
                            <div class="security-info mt-3">
                                <h4 class="section-subtitle"><i class="fas fa-shield-alt text-shell-yellow me-2"></i>Segurança</h4>

                                <div class="d-flex align-items-center justify-content-between mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="log-icon-small me-2">
                                            <i class="fas fa-sign-in-alt"></i>
                                        </div>
                                        <div>
                                            <span class="log-action-small">Login realizado</span>
                                            <small class="log-time-small d-block">Hoje às 15:04</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="alert-shell alert-shell-warning mb-3">
                                    <i class="fas fa-info-circle me-2"></i> Mantenha sua senha segura e não compartilhe com terceiros.
                                </div>
                            </div>

                            <div class="action-buttons mt-3">
                                <a href="/editar-perfil" class="btn-shell-red">
                                    <i class="fas fa-edit"></i> Editar
                                </a>
                                <a href="/minha-conta/alterar-senha" class="btn-shell">
                                    <i class="fas fa-key"></i> Senha
                                </a>

                            </div>
                        </div>
                    </div>
                </div>

                <!-- Card de Atividades -->
                <div class="col-lg-4 col-md-6">
                    <div class="card-shell activities-card">
                        <div class="card-header-shell">
                            <i class="fas fa-chart-line text-shell-yellow me-2"></i>
                            <h2 class="card-title">Atividades</h2>
                        </div>
                        <div class="card-body-shell">
                            {{ if and .User (eq .User.Role "filial") }}
                            <!-- Conteúdo específico para filiais -->
                            <div class="timers-container">
                                {{ if .activeTimers }}
                                    {{ range .activeTimers }}
                                    <div class="timer-card">
                                        <div class="timer-header">
                                            <h4 class="timer-title">{{ .EquipmentType }}</h4>
                                            <span class="badge-shell-red">ATIVO</span>
                                        </div>
                                        <div class="timer-info">
                                            <p><strong>Ordem:</strong> #{{ .MaintenanceOrderID }}</p>
                                            <p><strong>Início:</strong> {{ .StartTime.Format "02/01/2006 15:04" }}</p>
                                        </div>
                                        <div class="timer-display" data-start-time="{{ .StartTime }}" data-active="true">
                                            00:00:00
                                        </div>
                                    </div>
                                    {{ end }}
                                {{ else }}
                                    <div class="alert-shell alert-shell-warning">
                                        <i class="fas fa-info-circle me-2"></i> Sem manutenções ativas.
                                    </div>
                                {{ end }}
                            </div>
                            {{ else }}
                            <!-- Conteúdo para outros tipos de usuários -->
                            <div class="user-activities">
                                <div class="activity-item">
                                    <div class="activity-icon">
                                        <i class="fas fa-history"></i>
                                    </div>
                                    <div class="activity-content">
                                        <h4 class="activity-title">Última Atividade</h4>
                                        <div class="activity-value">
                                            {{ if .lastActivity }}
                                                {{ .lastActivity.Action }} - {{ .lastActivity.Timestamp.Format "02/01/2006 15:04" }}
                                            {{ else }}
                                                Login no sistema - {{ .now.Format "02/01/2006 15:04" }}
                                            {{ end }}
                                        </div>
                                    </div>
                                </div>

                                <div class="activity-item mt-3">
                                    <div class="activity-icon">
                                        <i class="fas fa-clipboard-list"></i>
                                    </div>
                                    <div class="activity-content">
                                        <h4 class="activity-title">Total de Ordens</h4>
                                        <div class="activity-value">{{ if .stats }}{{ .stats.TotalOrders }}{{ else }}0{{ end }}</div>
                                    </div>
                                </div>
                            </div>
                            {{ end }}

                            <!-- Notificações integradas ao card de atividades -->
                            <div class="notifications-preview mt-3">
                                <h4 class="section-subtitle"><i class="fas fa-bell text-shell-yellow me-2"></i>Notificações Recentes</h4>
                                {{ if .notifications }}
                                    {{ range .notifications }}
                                    <div class="notification-item-preview">
                                        <div class="notification-icon-small">
                                            <i class="fas {{ .Icon }}"></i>
                                        </div>
                                        <div class="notification-content-preview">
                                            <div class="notification-title-preview">{{ .Title }}</div>
                                            <div class="notification-time-preview">{{ .CreatedAt.Format "02/01/2006" }}</div>
                                        </div>
                                    </div>
                                    {{ end }}
                                {{ else }}
                                    <div class="empty-notifications-preview">
                                        <i class="fas fa-inbox me-2"></i>
                                        <span>Você não possui notificações no momento.</span>
                                    </div>
                                {{ end }}
                                <button class="btn-shell-red btn-sm mt-2 w-100" onclick="showModal('notificationsModal')">
                                    <i class="fas fa-bell me-2"></i>
                                    Ver Todas Notificações
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Card de Histórico -->
                <div class="col-lg-4 col-md-6">
                    <div class="card-shell history-card">
                        <div class="card-header-shell">
                            <i class="fas fa-history text-shell-yellow me-2"></i>
                            <h2 class="card-title">Histórico</h2>
                        </div>
                        <div class="card-body-shell">
                            {{ if and .User (eq .User.Role "filial") }}
                            <!-- Histórico para filiais -->
                            <div class="timers-container">
                                {{ if .recentTimers }}
                                    {{ range .recentTimers }}
                                    <div class="timer-card">
                                        <div class="timer-header">
                                            <h4 class="timer-title">{{ .EquipmentType }}</h4>
                                            <span class="badge-shell-green">CONCLUÍDO</span>
                                        </div>
                                        <div class="timer-info">
                                            <p><strong>Ordem:</strong> #{{ .MaintenanceOrderID }}</p>
                                            <p><strong>Período:</strong> {{ .StartTime.Format "02/01" }} à {{ .EndTime.Format "02/01/2006" }}</p>
                                        </div>
                                    </div>
                                    {{ end }}
                                {{ else }}
                                    <div class="alert-shell alert-shell-warning">
                                        <i class="fas fa-info-circle me-2"></i> Sem histórico de manutenções.
                                    </div>
                                {{ end }}
                            </div>
                            {{ else }}
                            <!-- Histórico para outros usuários -->
                            <div class="recent-orders">
                                <h3 class="section-subtitle">Ordens Recentes</h3>
                                {{ if .recentOrders }}
                                    <ul class="recent-orders-list">
                                        {{ range .recentOrders }}
                                        <li>
                                            <a href="/ordem/detalhes/{{ .ID }}" class="order-link">
                                                #{{ .ID }} - {{ .Title }}
                                            </a>
                                            <span class="order-date">{{ .CreatedAt.Format "02/01/2006" }}</span>
                                        </li>
                                        {{ end }}
                                    </ul>
                                {{ else }}
                                    <div class="alert-shell alert-shell-warning">
                                        <i class="fas fa-info-circle me-2"></i> Nenhuma ordem recente.
                                    </div>
                                {{ end }}
                            </div>
                            {{ end }}
                        </div>
                    </div>
                </div>



            </div>

            <!-- Seção de Equipamentos (apenas para filiais) - Ocupa toda a largura -->
            {{ if and .User .User.Role }}
                {{ if eq .User.Role "filial" }}
                <div class="mt-4">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h2 class="section-title"><i class="fas fa-tools text-shell-yellow me-2"></i>Equipamentos da Filial</h2>
                    </div>

                    <!-- Botão flutuante para adicionar novo equipamento -->
                    <div class="floating-action-button" id="btnNovoEquipamentoDireto">
                        <i class="fas fa-plus"></i>
                    </div>

                    <div class="row" id="equipmentCardContainer">
                        <!-- Cards de equipamentos serão inseridos aqui via JavaScript -->
                        <div class="col-12 text-center py-5">
                            <div class="spinner-border text-shell-yellow" role="status">
                                <span class="visually-hidden">Carregando...</span>
                            </div>
                            <p class="mt-3">Carregando equipamentos...</p>
                        </div>
                    </div>

                    <!-- Card de Transferência de Equipamentos -->
                    <div class="mt-4">
                        <div class="card-shell transfer-card" id="transferCard">
                            <div class="card-header-shell">
                                <i class="fas fa-exchange-alt text-shell-yellow me-2"></i>
                                <h2 class="card-title">Transferência de Equipamentos</h2>
                            </div>
                            <div class="card-body-shell">
                                <!-- Botão para iniciar transferência -->
                                <div class="d-flex justify-content-end mb-3">
                                    <button id="btnTransferirEquipamento" class="btn btn-shell-yellow">
                                        <i class="fas fa-plus me-2"></i>Nova Transferência
                                    </button>
                                </div>

                                <!-- Tabs para transferências enviadas e recebidas -->
                                <ul class="nav nav-tabs" id="transferTabs" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link active" id="pending-tab" data-bs-toggle="tab" data-bs-target="#pending-transfers" type="button" role="tab" aria-controls="pending-transfers" aria-selected="true">
                                            Transferências Enviadas
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="incoming-tab" data-bs-toggle="tab" data-bs-target="#incoming-transfers" type="button" role="tab" aria-controls="incoming-transfers" aria-selected="false">
                                            Transferências Recebidas
                                        </button>
                                    </li>
                                </ul>

                                <!-- Conteúdo das tabs -->
                                <div class="tab-content mt-3" id="transferTabsContent">
                                    <!-- Transferências enviadas -->
                                    <div class="tab-pane fade show active" id="pending-transfers" role="tabpanel" aria-labelledby="pending-tab">
                                        <div id="pendingTransfersContainer">
                                            <div class="text-center py-3">
                                                <div class="spinner-border text-shell-yellow" role="status">
                                                    <span class="visually-hidden">Carregando...</span>
                                                </div>
                                                <p class="mt-2">Carregando transferências...</p>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Transferências recebidas -->
                                    <div class="tab-pane fade" id="incoming-transfers" role="tabpanel" aria-labelledby="incoming-tab">
                                        <div id="incomingTransfersContainer">
                                            <div class="text-center py-3">
                                                <div class="spinner-border text-shell-yellow" role="status">
                                                    <span class="visually-hidden">Carregando...</span>
                                                </div>
                                                <p class="mt-2">Carregando transferências...</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Input oculto com o ID da filial atual -->
                    <input type="hidden" id="currentBranchId" value="{{ .User.BranchID }}">

                    <!-- Modal para solicitar transferência -->
                    <div class="modal fade" id="modalTransferirEquipamento" tabindex="-1" aria-labelledby="modalTransferirEquipamentoLabel" aria-hidden="true">
                        <div class="modal-dialog modal-dialog-centered">
                            <div class="modal-content bg-dark">
                                <div class="modal-header border-bottom border-secondary">
                                    <h5 class="modal-title text-shell-yellow">
                                        <i class="fas fa-exchange-alt me-2"></i>
                                        Nova Transferência de Equipamento
                                    </h5>
                                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Fechar"></button>
                                </div>
                                <div class="modal-body">
                                    <form id="formTransferirEquipamento">
                                        <!-- Equipamento -->
                                        <div class="mb-4">
                                            <label class="form-label text-shell-yellow">
                                                <i class="fas fa-tools me-2"></i>Equipamento
                                            </label>
                                            <select class="form-select bg-dark text-light border-secondary" id="equipmentSelect" required>
                                                <option value="">Selecione um equipamento</option>
                                            </select>
                                        </div>

                                        <!-- Filial de Destino -->
                                        <div class="mb-4">
                                            <label class="form-label text-shell-yellow">
                                                <i class="fas fa-building me-2"></i>Filial de Destino
                                            </label>
                                            <select class="form-select bg-dark text-light border-secondary" id="destinationBranchSelect" required>
                                                <option value="">Selecione uma filial</option>
                                            </select>
                                        </div>

                                        <!-- Justificativa -->
                                        <div class="mb-4">
                                            <label class="form-label text-shell-yellow">
                                                <i class="fas fa-comment-alt me-2"></i>Justificativa
                                            </label>
                                            <textarea class="form-control bg-dark text-light border-secondary"
                                                    id="justification"
                                                    rows="3"
                                                    required
                                                    placeholder="Descreva o motivo da transferência"></textarea>
                                        </div>

                                        <!-- Autorizado por -->
                                        <div class="mb-4">
                                            <label class="form-label text-shell-yellow">
                                                <i class="fas fa-user-check me-2"></i>Autorizado por
                                            </label>
                                            <input type="text"
                                                   class="form-control bg-dark text-light border-secondary"
                                                   id="authorizedBy"
                                                   required
                                                   placeholder="Nome do responsável pela autorização">
                                        </div>

                                        <!-- Alerta de informação -->
                                        <div class="alert-shell alert-shell-info mb-4">
                                            <i class="fas fa-info-circle me-2"></i>
                                            A transferência precisará ser aprovada pela filial de destino.
                                        </div>
                                    </form>
                                </div>
                                <div class="modal-footer border-top border-secondary">
                                    <button type="button" class="btn-shell" data-bs-dismiss="modal">
                                        <i class="fas fa-times me-2"></i>Cancelar
                                    </button>
                                    <button type="submit" form="formTransferirEquipamento" class="btn-shell-yellow">
                                        <i class="fas fa-paper-plane me-2"></i>Solicitar Transferência
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Modal para detalhes da transferência -->
                    <div class="modal fade" id="modalDetalhesTransferencia" tabindex="-1" aria-labelledby="modalDetalhesTransferenciaLabel" aria-hidden="true">
                        <div class="modal-dialog modal-dialog-centered">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="modalDetalhesTransferenciaLabel">Detalhes da Transferência</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="row mb-2">
                                        <div class="col-4 fw-bold">Equipamento:</div>
                                        <div class="col-8" id="detailsEquipmentName"></div>
                                    </div>
                                    <div class="row mb-2">
                                        <div class="col-4 fw-bold">Filial de Origem:</div>
                                        <div class="col-8" id="detailsSourceBranch"></div>
                                    </div>
                                    <div class="row mb-2">
                                        <div class="col-4 fw-bold">Filial de Destino:</div>
                                        <div class="col-8" id="detailsDestinationBranch"></div>
                                    </div>
                                    <div class="row mb-2">
                                        <div class="col-4 fw-bold">Solicitado por:</div>
                                        <div class="col-8" id="detailsRequestedBy"></div>
                                    </div>
                                    <div class="row mb-2">
                                        <div class="col-4 fw-bold">Data da Solicitação:</div>
                                        <div class="col-8" id="detailsRequestedAt"></div>
                                    </div>
                                    <div class="row mb-2">
                                        <div class="col-4 fw-bold">Status:</div>
                                        <div class="col-8" id="detailsStatus"></div>
                                    </div>
                                    <div class="row mb-2">
                                        <div class="col-4 fw-bold">Justificativa:</div>
                                        <div class="col-8" id="detailsJustification"></div>
                                    </div>
                                    <div class="row mb-2">
                                        <div class="col-4 fw-bold">Autorizado por:</div>
                                        <div class="col-8" id="detailsAuthorizedBy"></div>
                                    </div>
                                    <div class="row mb-2 d-none" id="detailsNotesContainer">
                                        <div class="col-4 fw-bold">Observações:</div>
                                        <div class="col-8" id="detailsNotes"></div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <script>
                        // Adicionar event listener ao botão assim que o DOM estiver carregado
                        document.addEventListener('DOMContentLoaded', function() {
                            // Botão para novo equipamento
                            const btnNovoEquipamentoDireto = document.getElementById('btnNovoEquipamentoDireto');
                            if (btnNovoEquipamentoDireto) {
                                btnNovoEquipamentoDireto.addEventListener('click', function() {
                                    console.log('Botão Novo Equipamento clicado');
                                    // Abrir diretamente o modal de novo equipamento
                                    const modalElement = document.getElementById('newEquipmentModal');
                                    if (modalElement) {
                                        const modal = new bootstrap.Modal(modalElement);
                                        modal.show();
                                    } else {
                                        console.error('Modal não encontrado: newEquipmentModal');
                                        alert('Erro ao abrir modal de novo equipamento. Por favor, recarregue a página.');
                                    }
                                });
                            }

                            // Botão para nova transferência
                            const btnTransferirEquipamento = document.getElementById('btnTransferirEquipamento');
                            if (btnTransferirEquipamento) {
                                btnTransferirEquipamento.addEventListener('click', function() {
                                    console.log('Botão Nova Transferência clicado');
                                    // Abrir o modal de transferência de equipamento
                                    const modalElement = document.getElementById('modalTransferirEquipamento');
                                    if (modalElement) {
                                        const modal = new bootstrap.Modal(modalElement);
                                        modal.show();
                                    } else {
                                        console.error('Modal não encontrado: modalTransferirEquipamento');
                                        alert('Erro ao abrir modal de transferência. Por favor, recarregue a página.');
                                    }
                                });
                            }

                            // Carregar equipamentos em cards responsivos
                            if (typeof window.loadEquipmentsAsCards === 'function') {
                                window.loadEquipmentsAsCards();
                            }
                        });
                    </script>
                </div>
                {{ end }}
            {{ end }}
            </div>
        </div>
    </div>

    <!-- Modal de Notificações -->
    <div class="modal fade" id="notificationsModal" tabindex="-1" aria-labelledby="notificationsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content modal-shell">
                <div class="modal-header">
                    <h5 class="modal-title" id="notificationsModalLabel">
                        <i class="fas fa-bell text-shell-yellow me-2"></i>
                        Minhas Notificações
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <div class="notifications-list">
                        {{ if .notifications }}
                            {{ range .notifications }}
                            <div class="notification-item">
                                <div class="notification-icon">
                                    <i class="fas {{ .Icon }}"></i>
                                </div>
                                <div class="notification-content">
                                    <div class="notification-title">{{ .Title }}</div>
                                    <div class="notification-message">{{ .Message }}</div>
                                    <div class="notification-time">{{ .CreatedAt.Format "02/01/2006 15:04" }}</div>
                                </div>
                            </div>
                            {{ end }}
                        {{ else }}
                            <div class="empty-notifications">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <p>Você não possui notificações no momento.</p>
                            </div>
                        {{ end }}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn-shell" data-bs-dismiss="modal">Fechar</button>
                    <button type="button" class="btn-shell-red">Marcar todas como lidas</button>
                </div>
            </div>
        </div>
    </div>



    <!-- Modal de Cadastro de Equipamento -->
    <div class="modal fade" id="newEquipmentModal" tabindex="-1" aria-labelledby="newEquipmentModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content modal-shell">
                <div class="modal-header">
                    <h5 class="modal-title" id="newEquipmentModalLabel">
                        <i class="fas fa-plus-circle text-shell-yellow me-2"></i>
                        Cadastrar Novo Equipamento
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <form id="equipmentForm">
                        <div class="mb-3">
                            <label for="equipmentName" class="form-label">Nome do Equipamento *</label>
                            <input type="text" class="form-control" id="equipmentName" name="name" required>
                        </div>

                        <div class="mb-3">
                            <label for="equipmentType" class="form-label">Tipo *</label>
                            <select class="form-select" id="equipmentType" name="type" required>
                                <option value="">Selecione o tipo</option>
                            </select>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="equipmentModel" class="form-label">Modelo</label>
                                <input type="text" class="form-control" id="equipmentModel" name="model">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="equipmentBrand" class="form-label">Fabricante</label>
                                <input type="text" class="form-control" id="equipmentBrand" name="brand">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="equipmentSerialNumber" class="form-label">Número de Série</label>
                            <input type="text" class="form-control" id="equipmentSerialNumber" name="serial_number">
                        </div>

                        <div class="mb-3">
                            <label for="equipmentStatus" class="form-label">Status *</label>
                            <select class="form-select" id="equipmentStatus" name="status" required>
                                <option value="ativo">Ativo</option>
                                <option value="manutencao">Em Manutenção</option>
                                <option value="inativo">Inativo</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="equipmentImage" class="form-label">Imagem do Equipamento</label>
                            <div class="custom-file-upload">
                                <input type="file" class="form-control" id="equipmentImage" name="image" accept="image/*">
                                <div class="image-preview mt-2" id="imagePreview">
                                    <img id="previewImg" src="/static/images/no-image.png" alt="Preview" style="max-width: 100%; max-height: 150px; display: none;">
                                </div>
                            </div>
                            <small class="form-text text-muted">Selecione uma imagem do equipamento (opcional).</small>
                        </div>

                        <div class="mb-3">
                            <label for="equipmentNotes" class="form-label">Observações</label>
                            <textarea class="form-control" id="equipmentNotes" name="notes" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn-shell" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn-shell-red" id="btnSalvarEquipamento">Salvar</button>
                    <script>
                        document.addEventListener('DOMContentLoaded', function() {
                            const btnSalvarEquipamento = document.getElementById('btnSalvarEquipamento');
                            if (btnSalvarEquipamento) {
                                btnSalvarEquipamento.addEventListener('click', function() {
                                    console.log('Botão Salvar Equipamento clicado');
                                    if (typeof window.saveEquipment === 'function') {
                                        window.saveEquipment();
                                    } else {
                                        console.error('Função saveEquipment não encontrada');
                                        alert('Erro ao salvar equipamento. Por favor, recarregue a página.');
                                    }
                                });
                            }
                        });
                    </script>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="/static/js/common.js"></script>
    <script src="/static/js/theme.js"></script>
    <script src="/static/js/sidebar.js"></script>
    <script src="/static/js/minha_conta.js"></script>
    <!-- Script específico para equipamentos -->
    {{ if and .User .User.Role }}
        {{ if eq .User.Role "filial" }}
        <script src="/static/js/equipamentos.js"></script>
        <script src="/static/js/equipment_transfer.js"></script>
        {{ end }}
    {{ end }}
</body>
</html>
{{ end }}
