package handlers

import (
	"fmt"
	"log"
	"net/http"
	"strconv"

	"tradicao/internal/models"
	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
)

// NotificationHandler manipula as rotas relacionadas a notificações
type NotificationHandler struct {
	notificationService *services.NotificationService
}

// NewNotificationHandler cria um novo NotificationHandler
func NewNotificationHandler(notificationService *services.NotificationService) *NotificationHandler {
	return &NotificationHandler{
		notificationService: notificationService,
	}
}

// RegisterRoutes registra as rotas de notificações no router do Gin
func (h *NotificationHandler) RegisterRoutes(router *gin.Engine) {
	api := router.Group("/api/notifications")
	{
		api.POST("/subscribe", h.Subscribe)
		api.POST("/unsubscribe", h.Unsubscribe)
		api.GET("/me", h.GetUserNotifications)
		api.PUT("/:id/read", h.<PERSON>)

		// Rota protegida para administradores/gerentes enviarem notificações
		api.POST("/send", AdminMiddleware(), h.SendNotification)
	}

	// Servir o Service Worker
	router.StaticFile("/service-worker.js", "./web/static/js/service-worker.js")
	router.GET("/vapid-public-key", h.GetVapidPublicKey)
}

// Subscribe manipula a assinatura de notificações push
func (h *NotificationHandler) Subscribe(c *gin.Context) {
	var requestData struct {
		Subscription models.PushSubscription `json:"subscription"`
		UserRole     string                  `json:"userRole"`
	}

	if err := c.BindJSON(&requestData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Formato de dados inválido"})
		return
	}

	// Obter o ID do usuário autenticado
	userID := GetUserIDFromContext(c)
	if userID == 0 {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Usuário não autenticado"})
		return
	}

	// Salvar a assinatura
	err := h.notificationService.SaveSubscription(userID, requestData.UserRole, requestData.Subscription)
	if err != nil {
		log.Printf("Erro ao salvar assinatura: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Falha ao salvar assinatura"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"success": true, "message": "Assinatura salva com sucesso"})
}

// Unsubscribe manipula o cancelamento de assinaturas
func (h *NotificationHandler) Unsubscribe(c *gin.Context) {
	var subscription struct {
		Subscription models.PushSubscription `json:"subscription"`
	}

	if err := c.BindJSON(&subscription); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Formato de dados inválido"})
		return
	}

	// Remover a assinatura pelo endpoint
	err := h.notificationService.DeleteSubscription(subscription.Subscription.Endpoint)
	if err != nil {
		log.Printf("Erro ao remover assinatura: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Falha ao remover assinatura"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"success": true, "message": "Assinatura removida com sucesso"})
}

// GetUserNotifications retorna as notificações do usuário autenticado
func (h *NotificationHandler) GetUserNotifications(c *gin.Context) {
	// Obter o ID do usuário autenticado
	userID := GetUserIDFromContext(c)
	if userID == 0 {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Usuário não autenticado"})
		return
	}

	// Parâmetros de paginação
	limitStr := c.DefaultQuery("limit", "20")
	offsetStr := c.DefaultQuery("offset", "0")

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 20
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		offset = 0
	}

	// Buscar notificações
	notifications, err := h.notificationService.GetUserNotifications(userID, limit, offset)
	if err != nil {
		log.Printf("Erro ao buscar notificações: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Falha ao buscar notificações"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"success": true, "notifications": notifications})
}

// MarkAsRead marca uma notificação como lida
func (h *NotificationHandler) MarkAsRead(c *gin.Context) {
	// Obter o ID do usuário autenticado
	userID := GetUserIDFromContext(c)
	if userID == 0 {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Usuário não autenticado"})
		return
	}

	// Obter o ID da notificação
	notificationID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID de notificação inválido"})
		return
	}

	// Marcar como lida
	err = h.notificationService.MarkNotificationAsRead(notificationID)
	if err != nil {
		log.Printf("Erro ao marcar notificação como lida: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Falha ao atualizar notificação"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"success": true})
}

// SendNotification permite que administradores/gerentes enviem notificações
func (h *NotificationHandler) SendNotification(c *gin.Context) {
	// Estrutura para receber os dados da notificação
	var requestData struct {
		Target  string `json:"target"` // user, role, all, order
		UserID  int64  `json:"userId,omitempty"`
		Role    string `json:"role,omitempty"`
		OrderID int64  `json:"orderId,omitempty"`
		Action  string `json:"action,omitempty"`
		Title   string `json:"title"`
		Message string `json:"message"`
		URL     string `json:"url,omitempty"`
	}

	if err := c.BindJSON(&requestData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Formato de dados inválido"})
		return
	}

	var err error

	// Obter o ID do usuário que está enviando a notificação
	senderID := GetUserIDFromContext(c)

	// Verificar o tipo de destino e enviar a notificação apropriada
	switch requestData.Target {
	case "user":
		// Enviar para um usuário específico
		if requestData.UserID <= 0 {
			c.JSON(http.StatusBadRequest, gin.H{"error": "ID de usuário inválido"})
			return
		}
		err = h.notificationService.EnviarNotificacao(
			requestData.UserID,
			requestData.Title,
			requestData.Message,
			requestData.OrderID,
			requestData.URL,
		)

	case "role":
		// Enviar para todos os usuários com um papel específico
		if requestData.Role == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Papel de usuário não especificado"})
			return
		}
		err = h.notificationService.EnviarNotificacaoPorPapel(
			requestData.Role,
			requestData.Title,
			requestData.Message,
			requestData.OrderID,
			requestData.URL,
		)

	case "order":
		// Enviar para pessoas relacionadas à ordem
		if requestData.OrderID <= 0 {
			c.JSON(http.StatusBadRequest, gin.H{"error": "ID de ordem inválido"})
			return
		}
		if requestData.Action == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Ação não especificada"})
			return
		}

		err = h.notificationService.EnviarNotificacaoOrdemServico(
			requestData.OrderID,
			requestData.Action,
			senderID,
			requestData.Title,
			requestData.Message,
		)

	default:
		c.JSON(http.StatusBadRequest, gin.H{"error": "Tipo de destino inválido"})
		return
	}

	if err != nil {
		log.Printf("Erro ao enviar notificação: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Falha ao enviar notificação: %v", err)})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Notificação enviada com sucesso",
	})
}

// GetVapidPublicKey retorna a chave pública VAPID para o cliente
func (h *NotificationHandler) GetVapidPublicKey(c *gin.Context) {
	// Essa chave deve ser configurada na inicialização do serviço
	// e obtida aqui de forma segura
	c.JSON(http.StatusOK, gin.H{
		"publicKey": "BLBx_FSiWPHNOkO5qfV5i5RHklvRTk8-BZ7Nc6YtQIObKBFsIl6JMpK8XkPT_SY0D_yoMURCkWcvBcFbNrOJTU4",
	})
}

// Função auxiliar para obter o ID do usuário do contexto
func GetUserIDFromContext(c *gin.Context) int64 {
	// Esta é uma implementação simplificada que deve ser
	// substituída pela lógica real de autenticação do sistema
	userID, exists := c.Get("userID")
	if !exists {
		return 0
	}

	id, ok := userID.(int64)
	if !ok {
		return 0
	}

	return id
}

// AdminMiddleware é um middleware fictício para verificar se o usuário é admin
func AdminMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Esta é uma implementação simplificada que deve ser
		// substituída pela lógica real de autorização do sistema
		userRole, exists := c.Get("userRole")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Não autorizado"})
			c.Abort()
			return
		}

		role, ok := userRole.(string)
		if !ok || (role != "admin" && role != "gerente" && role != "financeiro") {
			c.JSON(http.StatusForbidden, gin.H{"error": "Permissão negada"})
			c.Abort()
			return
		}

		c.Next()
	}
}
