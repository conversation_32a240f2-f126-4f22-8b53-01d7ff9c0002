package controllers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"tradicao/internal/models"
	"tradicao/internal/repository"
)

func ListMaintenanceOrders(c *gin.Context) {
	db := c.MustGet("db").(*gorm.DB)
	userIDStr := c.GetString("userID")
	userRole := c.GetString("userRole")
	userID, err := strconv.Atoi(userIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	filters := map[string]interface{}{}
	// Se não for admin, gerente ou financeiro, filtra por filial e equipamento
	if userRole != "admin" && userRole != "gerente" && userRole != "financeiro" {
		// Buscar equipamentos do técnico/prestador
		var techEquipments []models.TechnicianEquipment
		if err := db.Where("user_id = ?", userID).Find(&techEquipments).Error; err != nil {
			c.J<PERSON>(http.StatusInternalServerError, gin.H{"error": "Failed to fetch technician equipment"})
			return
		}
		equipmentTypes := make([]string, 0, len(techEquipments))
		for _, te := range techEquipments {
			equipmentTypes = append(equipmentTypes, te.EquipmentType)
		}
		var user models.User
		if err := db.First(&user, userID).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch user"})
			return
		}
		filters["branch_id"] = user.BranchID
		filters["equipment_type"] = equipmentTypes
	}

	repo := repository.NewMaintenanceOrderRepository(db)
	orders, _, err := repo.GetAll(c, filters, int64(userID), userRole, 1, 100)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch maintenance orders"})
		return
	}
	c.JSON(http.StatusOK, orders)
}
