package controllers

import (
	"net/http"
	"strconv"
	"time"
	"tradicao/internal/models"
	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
)

type MaintenanceOrderController struct {
	service *services.MaintenanceOrderService
}

func NewMaintenanceOrderController(service *services.MaintenanceOrderService) *MaintenanceOrderController {
	return &MaintenanceOrderController{service: service}
}

// GetByBranch retorna todas as ordens de manutenção de uma filial
func (c *MaintenanceOrderController) GetByBranch(ctx *gin.Context) {
	branchID, err := strconv.ParseUint(ctx.Param("branchID"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID da filial inválido"})
		return
	}

	userID := ctx.GetInt64("userID")
	userRole := ctx.GetString("userRole")

	orders, err := c.service.GetOrdersByBranch(ctx, uint(branchID), userID, userRole)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, orders)
}

// GetByID retorna uma ordem de manutenção pelo ID
func (c *MaintenanceOrderController) GetByID(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	// Bloquear acesso à ordem #18 que está causando problemas
	if id == 18 {
		ctx.JSON(http.StatusForbidden, gin.H{"error": "Esta ordem não está disponível no momento. Por favor, contate o suporte."})
		return
	}

	order, err := c.service.GetOrderByID(ctx, uint(id))
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, order)
}

// Create cria uma nova ordem de manutenção
func (c *MaintenanceOrderController) Create(ctx *gin.Context) {
	var order models.MaintenanceOrder
	if err := ctx.ShouldBindJSON(&order); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// ID será gerado automaticamente pelo banco de dados
	if err := c.service.CreateOrder(ctx, &order); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusCreated, order)
}

// Update atualiza uma ordem de manutenção existente
func (c *MaintenanceOrderController) Update(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	// Bloquear acesso à ordem #18 que está causando problemas
	if id == 18 {
		ctx.JSON(http.StatusForbidden, gin.H{"error": "Esta ordem não está disponível para atualização. Por favor, contate o suporte."})
		return
	}

	var order models.MaintenanceOrder
	if err := ctx.ShouldBindJSON(&order); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Usar o ID da URL
	order.ID = uint(id)
	if err := c.service.UpdateOrder(ctx, &order); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, order)
}

// Delete remove uma ordem de manutenção
func (c *MaintenanceOrderController) Delete(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	// Bloquear acesso à ordem #18 que está causando problemas
	if id == 18 {
		ctx.JSON(http.StatusForbidden, gin.H{"error": "Esta ordem não está disponível para exclusão. Por favor, contate o suporte."})
		return
	}

	if err := c.service.DeleteOrder(ctx, uint(id)); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"message": "Ordem de manutenção removida com sucesso"})
}

// GetByDateRange retorna ordens de manutenção por período
func (c *MaintenanceOrderController) GetByDateRange(ctx *gin.Context) {
	startDate, err := time.Parse("2006-01-02", ctx.Query("start_date"))
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Data inicial inválida"})
		return
	}

	endDate, err := time.Parse("2006-01-02", ctx.Query("end_date"))
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Data final inválida"})
		return
	}

	filters := map[string]interface{}{
		"start_date": startDate,
		"end_date":   endDate,
	}

	userID := ctx.GetInt64("userID")
	userRole := ctx.GetString("userRole")

	orders, _, err := c.service.GetAll(ctx, filters, userID, userRole, 1, 100)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, orders)
}

// GetByStatus retorna ordens de manutenção por status
func (c *MaintenanceOrderController) GetByStatus(ctx *gin.Context) {
	status := ctx.Param("status")
	userID := ctx.GetInt64("userID")
	userRole := ctx.GetString("userRole")

	orders, err := c.service.GetOrdersByStatus(ctx, status, userID, userRole)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, orders)
}

// GetByProvider retorna ordens de manutenção por prestador de serviço
func (c *MaintenanceOrderController) GetByProvider(ctx *gin.Context) {
	providerID, err := strconv.ParseUint(ctx.Param("providerID"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID do prestador inválido"})
		return
	}

	userID := ctx.GetInt64("userID")
	userRole := ctx.GetString("userRole")

	orders, err := c.service.GetOrdersByServiceProvider(ctx, uint(providerID), userID, userRole)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, orders)
}

// GetMetrics retorna métricas das ordens de manutenção
func (c *MaintenanceOrderController) GetMetrics(ctx *gin.Context) {
	userID := ctx.GetInt64("userID")
	userRole := ctx.GetString("userRole")

	metrics, err := c.service.GetMetrics(ctx, nil, userID, userRole)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, metrics)
}

// GetNotes retorna as notas de uma ordem de manutenção
func (c *MaintenanceOrderController) GetNotes(ctx *gin.Context) {
	orderID, err := strconv.ParseInt(ctx.Param("orderID"), 10, 64)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID da ordem inválido"})
		return
	}

	// Bloquear acesso à ordem #18 que está causando problemas
	if orderID == 18 {
		ctx.JSON(http.StatusForbidden, gin.H{"error": "Esta ordem não está disponível no momento. Por favor, contate o suporte."})
		return
	}

	order, err := c.service.GetOrderByID(ctx, uint(orderID))
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, order)
}

// AddMaterial adiciona um material a uma ordem de manutenção
func (c *MaintenanceOrderController) AddMaterial(ctx *gin.Context) {
	orderID, err := strconv.ParseInt(ctx.Param("id"), 10, 64)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID da ordem inválido"})
		return
	}

	// Bloquear acesso à ordem #18 que está causando problemas
	if orderID == 18 {
		ctx.JSON(http.StatusForbidden, gin.H{"error": "Esta ordem não está disponível para atualização. Por favor, contate o suporte."})
		return
	}

	var material models.MaterialRequest
	if err := ctx.ShouldBindJSON(&material); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	userID := ctx.GetInt64("userID")

	if err := c.service.AddMaterial(ctx, orderID, material, userID); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"message": "Material adicionado com sucesso"})
}

// UpdateStatus atualiza o status de uma ordem de manutenção
func (c *MaintenanceOrderController) UpdateStatus(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	// Bloquear acesso à ordem #18 que está causando problemas
	if id == 18 {
		ctx.JSON(http.StatusForbidden, gin.H{"error": "Esta ordem não está disponível para atualização. Por favor, contate o suporte."})
		return
	}

	status := ctx.Param("status")
	if status == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Status inválido"})
		return
	}

	userID := ctx.GetUint("userID")
	notes := ctx.PostForm("notes")

	if err := c.service.UpdateStatus(ctx, uint(id), models.OrderStatus(status), userID, notes); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"message": "Status atualizado com sucesso"})
}

// UpdatePriority atualiza a prioridade de uma ordem de manutenção
func (c *MaintenanceOrderController) UpdatePriority(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	// Bloquear acesso à ordem #18 que está causando problemas
	if id == 18 {
		ctx.JSON(http.StatusForbidden, gin.H{"error": "Esta ordem não está disponível para atualização. Por favor, contate o suporte."})
		return
	}

	priority := ctx.Param("priority")
	if priority == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Prioridade inválida"})
		return
	}

	userID := ctx.GetUint("userID")
	notes := ctx.PostForm("notes")

	if err := c.service.UpdatePriority(ctx, uint(id), models.PriorityLevel(priority), userID, notes); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"message": "Prioridade atualizada com sucesso"})
}

// UpdateDueDate atualiza a data de vencimento de uma ordem de manutenção
func (c *MaintenanceOrderController) UpdateDueDate(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	// Bloquear acesso à ordem #18 que está causando problemas
	if id == 18 {
		ctx.JSON(http.StatusForbidden, gin.H{"error": "Esta ordem não está disponível para atualização. Por favor, contate o suporte."})
		return
	}

	dueDate, err := time.Parse("2006-01-02", ctx.Param("dueDate"))
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Data inválida"})
		return
	}

	order, err := c.service.GetOrderByID(ctx, uint(id))
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	order.DueDate = dueDate
	if err := c.service.UpdateOrder(ctx, order); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"message": "Data de vencimento atualizada com sucesso"})
}

// UpdateTotalCost atualiza o custo total de uma ordem de manutenção
func (c *MaintenanceOrderController) UpdateTotalCost(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	// Bloquear acesso à ordem #18 que está causando problemas
	if id == 18 {
		ctx.JSON(http.StatusForbidden, gin.H{"error": "Esta ordem não está disponível para atualização. Por favor, contate o suporte."})
		return
	}

	var cost struct {
		TotalCost float64 `json:"total_cost"`
	}
	if err := ctx.ShouldBindJSON(&cost); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	order, err := c.service.GetOrderByID(ctx, uint(id))
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	order.ActualCost = cost.TotalCost
	if err := c.service.UpdateOrder(ctx, order); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"message": "Custo total atualizado com sucesso"})
}

// RemoveTestOrders remove permanentemente as ordens de teste do sistema
// Esta função só deve ser acessada por administradores
func (c *MaintenanceOrderController) RemoveTestOrders(ctx *gin.Context) {
	// Verificar se é administrador
	userRole := ctx.GetString("userRole")
	if userRole != "admin" {
		ctx.JSON(http.StatusForbidden, gin.H{"error": "Apenas administradores podem executar esta ação"})
		return
	}

	// Executar a remoção
	if err := c.service.RemoveTestOrders(); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao remover ordens de teste: " + err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"message": "Ordens de teste removidas com sucesso",
		"success": true,
	})
}
