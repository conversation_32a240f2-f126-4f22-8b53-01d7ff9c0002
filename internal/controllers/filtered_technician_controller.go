package controllers

import (
	"log"
	"net/http"
	"strconv"
	"tradicao/internal/models"
	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// FilteredTechnicianController é o controlador para buscar técnicos com filtros específicos
type FilteredTechnicianController struct {
	db                  *gorm.DB
	linkManagementService *services.LinkManagementService
}

// NewFilteredTechnicianController cria uma nova instância do controlador
func NewFilteredTechnicianController(db *gorm.DB, linkManagementService *services.LinkManagementService) *FilteredTechnicianController {
	return &FilteredTechnicianController{
		db:                  db,
		linkManagementService: linkManagementService,
	}
}

// GetTechniciansByProviderAndBranch retorna os técnicos vinculados a um prestador e a uma filial específica
func (c *FilteredTechnicianController) GetTechniciansByProviderAndBranch(ctx *gin.Context) {
	// Obter ID do prestador da URL
	providerIDStr := ctx.Param("provider_id")
	providerID, err := strconv.ParseUint(providerIDStr, 10, 64)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID do prestador inválido"})
		return
	}

	// Obter ID da filial da query string
	branchIDStr := ctx.Query("branch_id")
	if branchIDStr == "" {
		// Se não for fornecido, retornar todos os técnicos do prestador
		technicians, err := c.linkManagementService.GetProviderTechnicians(uint(providerID))
		if err != nil {
			ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao obter técnicos", "details": err.Error()})
			return
		}

		ctx.JSON(http.StatusOK, technicians)
		return
	}

	// Converter ID da filial para uint
	branchID, err := strconv.ParseUint(branchIDStr, 10, 64)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID da filial inválido"})
		return
	}

	log.Printf("[FILTERED-TECHNICIAN] Buscando técnicos para prestador %d e filial %d", providerID, branchID)

	// Buscar técnicos que estão vinculados tanto ao prestador quanto à filial
	var technicians []models.User

	// Consulta SQL para buscar técnicos vinculados ao prestador E à filial
	query := `
		SELECT u.* 
		FROM users u
		JOIN provider_technicians pt ON u.id = pt.technician_id
		JOIN technician_branches tb ON u.id = tb.technician_id
		WHERE pt.provider_id = ? 
		AND tb.branch_id = ?
		AND u.role IN ('technician', 'tecnico')
	`

	if err := c.db.Raw(query, providerID, branchID).Scan(&technicians).Error; err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar técnicos", "details": err.Error()})
		return
	}

	log.Printf("[FILTERED-TECHNICIAN] Encontrados %d técnicos para prestador %d e filial %d", len(technicians), providerID, branchID)

	ctx.JSON(http.StatusOK, technicians)
}
