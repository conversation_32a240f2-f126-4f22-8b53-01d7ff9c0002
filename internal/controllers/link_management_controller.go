package controllers

import (
	"bytes"
	"io"
	"log"
	"net/http"
	"strconv"
	"strings"
	"tradicao/internal/models"
	"tradicao/internal/repository"
	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
)

// LinkManagementController é o controlador para gerenciar vínculos entre filiais, prestadoras e técnicos
type LinkManagementController struct {
	service *services.LinkManagementService
}

// NewLinkManagementController cria uma nova instância do controlador
func NewLinkManagementController(service *services.LinkManagementService) *LinkManagementController {
	return &LinkManagementController{service: service}
}

// GetService retorna o serviço de gerenciamento de vínculos
func (c *LinkManagementController) GetService() *services.LinkManagementService {
	return c.service
}

// LinkProviderToBranch vincula um prestador a uma filial
func (c *LinkManagementController) LinkProviderToBranch(ctx *gin.Context) {
	var data struct {
		ProviderID uint `json:"provider_id" binding:"required"`
		BranchID   uint `json:"branch_id" binding:"required"`
	}
	if err := ctx.ShouldBindJSON(&data); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos", "details": err.Error()})
		return
	}

	if err := c.service.LinkProviderToBranch(data.ProviderID, data.BranchID); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao vincular prestador", "details": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"success": true, "message": "Prestador vinculado com sucesso"})
}

// UnlinkProviderFromBranch remove o vínculo entre um prestador e uma filial
func (c *LinkManagementController) UnlinkProviderFromBranch(ctx *gin.Context) {
	providerIDStr := ctx.Param("provider_id")
	providerID, err := strconv.ParseUint(providerIDStr, 10, 64)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID do prestador inválido"})
		return
	}

	branchIDStr := ctx.Param("branch_id")
	branchID, err := strconv.ParseUint(branchIDStr, 10, 64)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID da filial inválido"})
		return
	}

	if err := c.service.UnlinkProviderFromBranch(uint(providerID), uint(branchID)); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao desvincular prestador", "details": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"success": true, "message": "Prestador desvinculado com sucesso"})
}

// GetBranchProviders retorna os prestadores vinculados a uma filial
func (c *LinkManagementController) GetBranchProviders(ctx *gin.Context) {
	branchIDStr := ctx.Param("branch_id")
	branchID, err := strconv.ParseUint(branchIDStr, 10, 64)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID da filial inválido"})
		return
	}

	providers, err := c.service.GetBranchProviders(uint(branchID))
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao obter prestadores", "details": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, providers)
}

// LinkTechnicianToBranch vincula um técnico a uma filial
func (c *LinkManagementController) LinkTechnicianToBranch(ctx *gin.Context) {
	var data struct {
		TechnicianID uint `json:"technician_id" binding:"required"`
		BranchID     uint `json:"branch_id" binding:"required"`
		SpecialtyID  uint `json:"specialty_id" binding:"required"`
	}
	if err := ctx.ShouldBindJSON(&data); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos", "details": err.Error()})
		return
	}

	if err := c.service.LinkTechnicianToBranch(data.TechnicianID, data.BranchID, data.SpecialtyID); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao vincular técnico", "details": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"success": true, "message": "Técnico vinculado com sucesso"})
}

// UnlinkTechnicianFromBranch remove o vínculo entre um técnico e uma filial
func (c *LinkManagementController) UnlinkTechnicianFromBranch(ctx *gin.Context) {
	technicianIDStr := ctx.Param("technician_id")
	technicianID, err := strconv.ParseUint(technicianIDStr, 10, 64)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID do técnico inválido"})
		return
	}

	branchIDStr := ctx.Param("branch_id")
	branchID, err := strconv.ParseUint(branchIDStr, 10, 64)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID da filial inválido"})
		return
	}

	if err := c.service.UnlinkTechnicianFromBranch(uint(technicianID), uint(branchID)); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao desvincular técnico", "details": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"success": true, "message": "Técnico desvinculado com sucesso"})
}

// GetBranchTechnicians retorna os técnicos vinculados a uma filial
func (c *LinkManagementController) GetBranchTechnicians(ctx *gin.Context) {
	branchIDStr := ctx.Param("branch_id")
	branchID, err := strconv.ParseUint(branchIDStr, 10, 64)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID da filial inválido"})
		return
	}

	technicians, err := c.service.GetBranchTechnicians(uint(branchID))
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao obter técnicos", "details": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, technicians)
}

// GetProviderTechnicians retorna os técnicos vinculados a um prestador
func (c *LinkManagementController) GetProviderTechnicians(ctx *gin.Context) {
	providerIDStr := ctx.Param("provider_id")
	providerID, err := strconv.ParseUint(providerIDStr, 10, 64)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID do prestador inválido"})
		return
	}

	technicians, err := c.service.GetProviderTechnicians(uint(providerID))
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao obter técnicos", "details": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, technicians)
}

// GetAllProviderBranchLinks retorna todos os vínculos entre prestadores e filiais
func (c *LinkManagementController) GetAllProviderBranchLinks(ctx *gin.Context) {
	links, err := c.service.GetAllProviderBranchLinks()
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Erro ao obter vínculos entre prestadores e filiais",
			"details": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"success": true,
		"links":   links,
	})
}

// GetAllTechnicianBranchLinks retorna todos os vínculos entre técnicos e filiais
func (c *LinkManagementController) GetAllTechnicianBranchLinks(ctx *gin.Context) {
	links, err := c.service.GetAllTechnicianBranchLinks()
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Erro ao obter vínculos entre técnicos e filiais",
			"details": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"success": true,
		"links":   links,
	})
}

// O método RenderLinkManagementPage foi removido

// GetAllProviderTechnicianLinks retorna todos os vínculos entre prestadoras e técnicos
func (c *LinkManagementController) GetAllProviderTechnicianLinks(ctx *gin.Context) {
	links, err := c.service.GetAllProviderTechnicianLinks()
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Erro ao obter vínculos entre prestadoras e técnicos",
			"details": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"success": true,
		"links":   links,
	})
}

// LinkProviderToTechnician vincula uma prestadora a um técnico
func (c *LinkManagementController) LinkProviderToTechnician(ctx *gin.Context) {
	log.Printf("[LINK-MANAGEMENT] Iniciando vinculação de prestadora a técnico")

	var data struct {
		ProviderID   uint `json:"provider_id" binding:"required"`
		TechnicianID uint `json:"technician_id" binding:"required"`
	}

	// Log dos dados recebidos
	bodyBytes, err := io.ReadAll(ctx.Request.Body)
	if err != nil {
		log.Printf("[LINK-MANAGEMENT-ERROR] Erro ao ler corpo da requisição: %v", err)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Erro ao ler dados da requisição", "details": err.Error()})
		return
	}

	// Restaurar o corpo da requisição para que possa ser lido novamente
	ctx.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	// Log dos dados recebidos
	log.Printf("[LINK-MANAGEMENT] Dados recebidos: %s", string(bodyBytes))

	if err := ctx.ShouldBindJSON(&data); err != nil {
		log.Printf("[LINK-MANAGEMENT-ERROR] Erro na validação dos dados: %v", err)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos", "details": err.Error()})
		return
	}

	log.Printf("[LINK-MANAGEMENT] Vinculando prestadora ID %d ao técnico ID %d", data.ProviderID, data.TechnicianID)

	// Verificar se a prestadora existe
	providerRepo := repository.NewGormServiceProviderRepository()
	provider, err := providerRepo.FindByID(data.ProviderID)
	if err != nil {
		log.Printf("[LINK-MANAGEMENT-ERROR] Erro ao buscar prestadora ID %d: %v", data.ProviderID, err)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Prestadora não encontrada", "details": err.Error()})
		return
	}

	if provider.ID == 0 {
		log.Printf("[LINK-MANAGEMENT-ERROR] Prestadora ID %d não existe", data.ProviderID)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Prestadora não encontrada"})
		return
	}

	// Verificar se o técnico existe
	userRepo := repository.NewGormUserRepository()
	technician, err := userRepo.FindByID(data.TechnicianID)
	if err != nil {
		log.Printf("[LINK-MANAGEMENT-ERROR] Erro ao buscar técnico ID %d: %v", data.TechnicianID, err)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Técnico não encontrado", "details": err.Error()})
		return
	}

	if technician.ID == 0 {
		log.Printf("[LINK-MANAGEMENT-ERROR] Técnico ID %d não existe", data.TechnicianID)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Técnico não encontrado"})
		return
	}

	// Verificar se o usuário é realmente um técnico
	if !models.IsTechnician(string(technician.Role)) {
		log.Printf("[LINK-MANAGEMENT-ERROR] Usuário ID %d não é um técnico (role: %s)", data.TechnicianID, technician.Role)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Usuário não é um técnico"})
		return
	}

	if err := c.service.LinkProviderToTechnician(data.ProviderID, data.TechnicianID); err != nil {
		log.Printf("[LINK-MANAGEMENT-ERROR] Erro ao vincular prestadora %d ao técnico %d: %v",
			data.ProviderID, data.TechnicianID, err)

		// Verificar se é um erro de vínculo já existente
		if strings.Contains(err.Error(), "já está vinculado") {
			ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao vincular prestadora ao técnico", "details": err.Error()})
		return
	}

	log.Printf("[LINK-MANAGEMENT] Prestadora ID %d vinculada ao técnico ID %d com sucesso",
		data.ProviderID, data.TechnicianID)
	ctx.JSON(http.StatusOK, gin.H{"success": true, "message": "Prestadora vinculada ao técnico com sucesso"})
}

// UnlinkProviderFromTechnician remove o vínculo entre uma prestadora e um técnico
func (c *LinkManagementController) UnlinkProviderFromTechnician(ctx *gin.Context) {
	providerIDStr := ctx.Param("provider_id")
	providerID, err := strconv.ParseUint(providerIDStr, 10, 64)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID da prestadora inválido"})
		return
	}

	technicianIDStr := ctx.Param("technician_id")
	technicianID, err := strconv.ParseUint(technicianIDStr, 10, 64)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID do técnico inválido"})
		return
	}

	if err := c.service.UnlinkProviderFromTechnician(uint(providerID), uint(technicianID)); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao desvincular prestadora do técnico", "details": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"success": true, "message": "Prestadora desvinculada do técnico com sucesso"})
}

// InheritBranchLinks herda os vínculos de filiais de uma prestadora para um técnico
func (c *LinkManagementController) InheritBranchLinks(ctx *gin.Context) {
	var data struct {
		ProviderID   uint `json:"provider_id" binding:"required"`
		TechnicianID uint `json:"technician_id" binding:"required"`
	}
	if err := ctx.ShouldBindJSON(&data); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos", "details": err.Error()})
		return
	}

	count, err := c.service.InheritBranchLinksFromProvider(data.ProviderID, data.TechnicianID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao herdar vínculos de filiais", "details": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Vínculos de filiais herdados com sucesso",
		"count":   count,
	})
}
