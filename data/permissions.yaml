# Sistema de Permissões Centralizado
# Rede Tradição - Sistema de Manutenção

roles:
    admin:
        description: Administrador do Sistema
        pages:
            - '*'
        apis:
            - '*'
            - api/maintenance-orders/remove-test-orders
    branch_user:
        description: ""
        pages:
            - orders
        apis:
            - api/maintenance-orders
    filial:
        description: Gestor de Filial/Posto
        pages:
            - dashboard
            - calendario-flip
            - manutencao
            - nova-manutencao
            - minha-conta
            - orders
            - maintenance/view
            - ordemtecnico
            - manutencaoordem
        apis:
            - api/user/me
            - api/auth/logout
            - api/equipments
            - api/equipments/:id
            - api/equipments/types
            - api/equipments/filial/:id
            - api/maintenance
            - api/maintenance/:id
            - api/stations
            - api/notifications/settings
            - api/notifications/subscribe
            - api/ordens/:id/manutencao
            - api/ordens/:id/custos
            - api/ordens/:id/cronograma
            - api/ordens/:id/chat
            - api/ordens/:id
    financeiro:
        description: Analista Financeiro
        pages:
            - dashboard
            - dashboard-calendario
            - calendario-avancado
            - calendario-flip
            - manutencao
            - financeiro
            - finance/dashboard
            - settings
            - minha-conta
            - relatorios
            - orders
            - maintenance/view
            - dashboard/orders
            - galeria
            - postos
        apis:
            - api/user/me
            - api/auth/logout
            - api/equipments
            - api/equipments/:id
            - api/equipments/types
            - api/equipments/filial/:id
            - api/maintenance
            - api/maintenance/:id
            - api/dashboard/metrics
            - api/dashboard/recent
            - api/dashboard/status
            - api/reports
            - api/reports/download
            - api/financial/summary
            - api/financial/transactions
            - api/notifications/settings
            - api/notifications/subscribe
    gerente:
        description: Gerente / Gestor de Manutenção
        pages:
            - dashboard
            - dashboard-enhanced
            - dashboard-calendario
            - calendario-avancado
            - calendario-flip
            - calendario/stats
            - manutencao
            - nova-manutencao
            - settings
            - minha-conta
            - relatorios
            - orders
            - maintenance/new
            - maintenance/edit
            - maintenance/view
            - dashboard/orders
            - dashboard/approval
            - galeria
            - postos
        apis:
            - api/user/me
            - api/auth/logout
            - api/equipments
            - api/equipments/:id
            - api/equipments/types
            - api/equipments/filial/:id
            - api/maintenance
            - api/maintenance/:id
            - api/stations
            - api/dashboard/metrics
            - api/dashboard/recent
            - api/dashboard/status
            - api/reports
            - api/reports/download
            - api/notifications/settings
            - api/notifications/subscribe
    provider:
        description: Prestador de Serviço
        pages:
            - dashboard
            - calendario-flip
            - minha-conta
            - maintenance/view
            - galeria
            - postos
            - ordemtecnica
            - ordemtecnico
            - manutencaoordem
            - prestadoras/minha_equipe
            - prestadoras/perfil_empresa
        apis:
            - api/user/me
            - api/auth/logout
            - api/equipments
            - api/equipments/:id
            - api/equipments/types
            - api/equipments/filial/:id
            - api/maintenance/:id
            - api/notifications/settings
            - api/notifications/subscribe
            - api/my-provider
            - api/my-provider/technicians
            - api/my-provider/logo
            - api/providers
            - api/providers/:id
            - api/providers/:id/technicians
            - api/providers/:id/logo
            - api/ordens/:id/manutencao
            - api/ordens/:id/custos
            - api/ordens/:id/cronograma
            - api/ordens/:id/chat
            - api/ordens/:id
    technician:
        description: "Técnico de Manutenção"
        pages:
            - ordemtecnica
            - ordemtecnico
            - manutencaoordem
            - dashboard
            - postos
            - minha-conta
            - galeria
            - calendario-flip
        apis:
            - api/maintenance-orders
            - api/maintenance-orders/:id
            - api/user/me
            - api/auth/logout
            - api/technicians/me/provider
            - api/technicians/me/orders
            - api/user/avatar
            - api/users/:id/avatar
            - api/ordens/:id/manutencao
            - api/ordens/:id/custos
            - api/ordens/:id/cronograma
            - api/ordens/:id/chat
            - api/ordens/:id
            - api/equipments
            - api/equipments/:id
            - api/equipments/types
            - api/equipments/filial/:id
            - api/filiais
    tecnico:
        description: "Técnico de Manutenção (Alias)"
        pages:
            - ordemtecnica
            - ordemtecnico
            - manutencaoordem
            - dashboard
            - postos
            - minha-conta
            - galeria
            - calendario-flip
        apis:
            - api/maintenance-orders
            - api/maintenance-orders/:id
            - api/user/me
            - api/auth/logout
            - api/technicians/me/provider
            - api/technicians/me/orders
            - api/user/avatar
            - api/users/:id/avatar
            - api/ordens/:id/manutencao
            - api/ordens/:id/custos
            - api/ordens/:id/cronograma
            - api/ordens/:id/chat
            - api/ordens/:id
            - api/equipments
            - api/equipments/:id
            - api/equipments/types
            - api/equipments/filial/:id
            - api/filiais
public_pages:
    - ""
    - login
    - logout
    - acesso_negado
    - change-password
public_apis:
    - api/login
    - api/auth/change-password
    - api/uploads
